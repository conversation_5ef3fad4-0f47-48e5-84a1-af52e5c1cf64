{"permissions": {"allow": ["Bash(ls:*)", "Bash(npm run test:cov)", "Bash(rm:*)", "Bash(npm test:*)", "Bash(npm run lint:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npx eslint:*)", "Bash(npx tsc:*)", "Bash(npm install:*)", "mcp__serena__find_symbol", "Bash(git checkout:*)", "mcp__serena__replace_regex", "mcp__serena__activate_project", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__get_symbols_overview", "mcp__serena__write_memory", "mcp__serena__search_for_pattern", "mcp__serena__replace_symbol_body"], "deny": []}}