import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";

@Injectable()
export class DepartmentRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findAll() {
    return this.prisma.department.findMany({
      orderBy: { name: "asc" },
    });
  }

  public async findOne(id: string) {
    return this.prisma.department.findUnique({
      where: { id },
    });
  }
}
