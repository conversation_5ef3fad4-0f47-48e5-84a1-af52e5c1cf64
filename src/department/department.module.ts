import { Module } from "@nestjs/common";
import { PrismaService } from "../prisma.service";
import { DepartmentService } from "./department.service";
import { DepartmentRepository } from "./repositories/department.repository";
import { AuthModule } from "@/auth/auth.module";
import { AuthService } from "@/auth/auth.service";

@Module({
  exports: [DepartmentService, DepartmentRepository],
  imports: [AuthModule],
  providers: [DepartmentService, DepartmentRepository, AuthService, PrismaService],
})
export class DepartmentModule {}
