import { Test, TestingModule } from "@nestjs/testing";
import { RequestQueryDto, CreateRequestDto } from "./dto";
import { REQUEST_ACTION } from "./dto/request-action.enum";
import { RequestController } from "./request.controller";
import { RequestService } from "./request.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard } from "@/auth/role.guard";

describe("RequestController", () => {
  let controller: RequestController;
  let service: RequestService;

  beforeEach(async () => {
    const serviceMock = {
      create: jest.fn().mockResolvedValue({ id: "r1", status: "PENDING_APPROVAL" }),
      findAll: jest.fn().mockResolvedValue({ data: [], meta: { limit: 10, page: 1, total: 0 } }),
      handleAction: jest.fn().mockResolvedValue({ id: "r1", status: "APPROVED" }),
      revokeAccess: jest.fn(),
    };
    const moduleReference: TestingModule = await Test.createTestingModule({
      controllers: [RequestController],
      providers: [
        { provide: RequestService, useValue: serviceMock },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(RoleGuard)
      .useValue({ canActivate: () => true })
      .compile();
    controller = moduleReference.get<RequestController>(RequestController);
    service = moduleReference.get<RequestService>(RequestService);
  });

  it("should get all requests", async () => {
    const query: RequestQueryDto = { limit: 10, page: 1, search: "", status: undefined };
    const result = await controller.findAll(query);
    expect(service.findAll).toHaveBeenCalledWith(query);
    expect(result).toEqual({ data: [], meta: { limit: 10, page: 1, total: 0 } });
  });

  describe("create", () => {
    it("should create a request", async () => {
      const createRequestDto: CreateRequestDto = { formulationId: "f1" };
      const request = { headers: { oid: "test-user-id" }, user: { oid: "test-user-id" } };
      const result = await controller.create(createRequestDto, request);
      expect(service.create).toHaveBeenCalledWith("test-user-id", createRequestDto);
      expect(result).toEqual({ id: "r1", status: "PENDING_APPROVAL" });
    });

    it("should throw an error if user id is missing", async () => {
      const createRequestDto: CreateRequestDto = { formulationId: "f1" };
      const request = { headers: {oid: ""}, user: {oid: ""} };
      await expect(controller.create(createRequestDto, request)).rejects.toThrow("User ID is missing from request context. Make sure authentication is working and user is attached to request.");
    });
  });

  describe("handleAction", () => {
    it("should handle request action", async () => {
      const actionDto = { action: REQUEST_ACTION.APPROVE };
      const request = { headers: { oid: "test-user-id" }, user: { oid: "test-user-id" } };
      const result = await controller.handleAction(actionDto, request, "r1");
      expect(service.handleAction).toHaveBeenCalledWith("test-user-id", "r1", REQUEST_ACTION.APPROVE);
      expect(result).toEqual({ id: "r1", status: "APPROVED" });
    });

    it("should throw an error if user id is missing", async () => {
      const actionDto = { action: REQUEST_ACTION.APPROVE };
      const request = { headers: { oid: ""}, user: { oid: ""} };
      await expect(controller.handleAction(actionDto, request, "r1")).rejects.toThrow("User ID is missing from request context. Make sure authentication is working and user is attached to request.");
    });
  });

  describe("revokeAccess", () => {
    it("should revoke access", async () => {
      const request = { headers: { oid: "test-user-id" }, user: { oid: "test-user-id" } };
      await controller.revokeAccess("r1", request);
      expect(service.revokeAccess).toHaveBeenCalledWith("test-user-id", "r1");
    });

    it("should throw an error if user id is missing", async () => {
      const request = { headers: {oid: ""}, user: {oid: ""} };
      await expect(controller.revokeAccess("r1", request)).rejects.toThrow("User ID is missing from request context.");
    });
  });
});
