import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { EmailModule } from "../email/email.module";
import { NotificationModule } from "../notification/notification.module";
import { PrismaService } from "../prisma.service";
import { RoleModule } from "../role/role.module";
import { UserModule } from "../user/user.module";
import { RequestRepository } from "./repositories";
import { SearchQueryBuilder } from "./repositories/search-query.builder";
import { RequestController } from "./request.controller";
import { RequestService } from "./request.service";

@Module({
  controllers: [RequestController],
  imports: [AuthModule, EmailModule, NotificationModule, UserModule, RoleModule],
  providers: [RequestService, RequestRepository, SearchQueryBuilder, PrismaService],
})
export class RequestModule {}
