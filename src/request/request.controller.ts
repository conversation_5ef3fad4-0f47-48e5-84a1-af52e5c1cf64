import { Controller, Get, Post, Body, Query, HttpStatus, HttpCode, UseGuards, Request as Request_, Param, Delete } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOAuth2,
  <PERSON>pi<PERSON><PERSON><PERSON>,
} from "@nestjs/swagger";
import { UserRole } from "../role/role.types";
import { RequestQueryDto, PaginatedRequestResponseDto, CreateRequestDto, RequestResponseDto } from "./dto";
import { RequestActionDto } from "./dto/request-action.dto";
import { RequestService } from "./request.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard, ROLES } from "@/auth/role.guard";
import { ErrorResponseDto } from "@/common/dto";
import { RequestStatus } from "@/generated/prisma";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("requests")
@Controller("requests")
export class RequestController {
  public constructor(private readonly requestService: RequestService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.ENGINEERING_MANAGER,
  )
  @ApiOperation({
    description: "Retrieve all requests with optional filtering by search term and status. Available to ADMIN and Engineering Manager roles only.",
    summary: "Get all requests",
  })
  @ApiQuery({
    description: "Search across name, email, location, department, and formulation name + grade",
    example: "bumper A",
    name: "search",
    required: false,
  })
  @ApiQuery({
    description: "Filter by request status",
    enum: RequestStatus,
    example: RequestStatus.PENDING_APPROVAL,
    name: "status",
    required: false,
  })
  @ApiQuery({
    description: "Page number",
    example: 1,
    name: "page",
    required: false,
  })
  @ApiQuery({
    description: "Number of items per page",
    example: 10,
    name: "limit",
    required: false,
  })
  @ApiResponse({
    description: "Requests retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedRequestResponseDto,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Forbidden - User not authorized to access requests",
    status: HttpStatus.FORBIDDEN,
    type: ErrorResponseDto,
  })
  public async findAll(@Query() query: RequestQueryDto): Promise<PaginatedRequestResponseDto> {
    return this.requestService.findAll(query);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ROLES(
    UserRole.ADMIN,
    UserRole.ENGINEERS
  )
  @ApiOperation({
    description: "Create a new request for access to a formulation. Available to all authenticated users.",
    summary: "Create a new request",
  })
  @ApiResponse({
    description: "Request created successfully",
    status: HttpStatus.CREATED,
    type: RequestResponseDto,
  })
  @ApiResponse({
    description: "Bad request - Invalid input data",
    status: HttpStatus.BAD_REQUEST,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Not found - Formulation not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Conflict - Request already exists for this user and formulation",
    status: HttpStatus.CONFLICT,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
    type: ErrorResponseDto,
  })
  public async create(
    @Body() createRequestDto: CreateRequestDto,
    @Request_() request: { user?: { oid: string }, headers?: { oid: string } }
  ): Promise<RequestResponseDto> {
    const userId = request.user?.oid ?? request.headers?.oid;
    if (!userId) {
      throw new Error("User ID is missing from request context. Make sure authentication is working and user is attached to request.");
    }
    return this.requestService.create(userId, createRequestDto);
  }

  @Post(":id/actions")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.ENGINEERING_MANAGER,
  )
  @ApiOperation({
    description: "Approve or reject a request by action. Available to ADMIN and Engineering Manager roles only.",
    summary: "Approve or reject a request",
  })
  @ApiResponse({
    description: "Request status updated successfully",
    status: HttpStatus.OK,
    type: RequestResponseDto,
  })
  @ApiResponse({
    description: "Bad request - Invalid action or status",
    status: HttpStatus.BAD_REQUEST,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Not found - Request not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  public async handleAction(
    @Body() actionDto: RequestActionDto,
    @Request_() request: { user?: { oid: string }, headers?: { oid: string } },
    @Param("id") id: string,
  ): Promise<RequestResponseDto> {
    const userId = request.user?.oid ?? request.headers?.oid;
    if (!userId) {
      throw new Error("User ID is missing from request context. Make sure authentication is working and user is attached to request.");
    }
    return this.requestService.handleAction(userId, id, actionDto.action);
  }

  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ROLES(
    UserRole.ADMIN,
    UserRole.ENGINEERING_MANAGER,
  )
  @ApiOperation({ summary: "Revoke (delete) access request" })
  @ApiResponse({ description: "Request deleted (access revoked)", status: HttpStatus.NO_CONTENT })
  public async revokeAccess(
    @Param("id") id: string,
    @Request_() request: { user?: { oid: string }, headers?: { oid: string } }
  ): Promise<void> {
    const userId = request.user?.oid ?? request.headers?.oid;
    if (!userId) {
      throw new Error("User ID is missing from request context.");
    }
    await this.requestService.revokeAccess(userId, id);
  }
}
