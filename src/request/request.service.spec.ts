import { BadRequestException, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { TestingModule, Test } from "@nestjs/testing";
import { REQUEST_ACTION } from "./dto/request-action.enum";
import { RequestRepository } from "./repositories";
import { RequestService } from "./request.service";
import { EmailService } from "@/email/email.service";
import { RequestStatus } from "@/generated/prisma";
import { NotificationService } from "@/notification";
import { PrismaService } from "@/prisma.service";
import { RoleService } from "@/role";
import { UserService } from "@/user";

class MockEventEmitter {
  emit() {
    return null;
  }
}

describe("RequestService", () => {
  let service: RequestService;
  let requestRepository: any;
  let prisma: any;
  let userService: any;
  let roleService: any;
  let eventEmitter: EventEmitter2;

  const mockRequest = {
    id: "r1",
    status: RequestStatus.PENDING_APPROVAL,
    requester: { id: "user1" },
    formulation: { id: "f1", name: "FormA" },
    requesterId: "user1",
  };

  beforeEach(async () => {
    requestRepository = {
      findAll: jest.fn().mockResolvedValue({ data: [mockRequest], total: 1 }),
      create: jest.fn().mockResolvedValue(mockRequest),
      findById: jest.fn().mockResolvedValue(mockRequest),
      updateStatus: jest.fn().mockImplementation((id, status) => Promise.resolve({ ...mockRequest, status })),
      delete: jest.fn(),
    };
    prisma = { formulation: { findUnique: jest.fn() } };
    userService = { findAll: jest.fn() };
    roleService = { findByCode: jest.fn() };

    const moduleReference: TestingModule = await Test.createTestingModule({
      providers: [
        { provide: RequestRepository, useValue: requestRepository },
        { provide: PrismaService, useValue: prisma },
        { provide: EmailService, useValue: {} },
        { provide: NotificationService, useValue: {} },
        { provide: UserService, useValue: userService },
        { provide: RoleService, useValue: roleService },
        { provide: EventEmitter2, useClass: MockEventEmitter },
        RequestService,
      ],
    }).compile();
    service = moduleReference.get<RequestService>(RequestService);
    eventEmitter = moduleReference.get<EventEmitter2>(EventEmitter2);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("findAll", () => {
    it("should return paginated requests", async () => {
      const result = await service.findAll({});
      expect(requestRepository.findAll).toHaveBeenCalled();
      expect(result.data[0].id).toBe("r1");
    });
  });

  describe("create", () => {
    it("should throw if formulation not found", async () => {
      prisma.formulation.findUnique.mockResolvedValue(null);
      await expect(service.create("user1", { formulationId: "f1" })).rejects.toThrow("Formulation not found");
    });

    it("should create request and emit events for email/notification", async () => {
      prisma.formulation.findUnique.mockResolvedValue({ id: "f1", name: "FormA" });
      roleService.findByCode.mockResolvedValue({ id: "role1" });
      userService.findAll.mockResolvedValue({ data: [{ id: "manager1", email: "<EMAIL>" }] });
      jest.spyOn(eventEmitter, "emit");

      const result = await service.create("user1", { formulationId: "f1" });
      expect(result.id).toBe("r1");
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        "request.new-formulation",
        expect.objectContaining({
          formulationName: "FormA",
          managerEmails: ["<EMAIL>"],
          managerIds: ["manager1"],
          requesterId: "user1",
          message: expect.any(String),
          html: expect.any(String),
        })
      );
    });
  });

  describe("handleAction", () => {
    it("should approve a request", async () => {
      jest.spyOn(eventEmitter, "emit");
      const result = await service.handleAction("user1", "r1", REQUEST_ACTION.APPROVE);
      expect(requestRepository.updateStatus).toHaveBeenCalledWith("r1", RequestStatus.APPROVED);
      expect(result.status).toBe(RequestStatus.APPROVED);
      expect(eventEmitter.emit).toHaveBeenCalledWith("request.status-changed", expect.any(Object));
    });

    it("should reject a request", async () => {
      jest.spyOn(eventEmitter, "emit");
      const result = await service.handleAction("user1", "r1", REQUEST_ACTION.REJECT);
      expect(requestRepository.updateStatus).toHaveBeenCalledWith("r1", RequestStatus.REJECTED);
      expect(result.status).toBe(RequestStatus.REJECTED);
      expect(eventEmitter.emit).toHaveBeenCalledWith("request.status-changed", expect.any(Object));
    });

    it("should throw NotFoundException if request not found", async () => {
      requestRepository.findById.mockResolvedValue(null);
      await expect(service.handleAction("user1", "r2", REQUEST_ACTION.APPROVE)).rejects.toThrow(NotFoundException);
    });

    it("should throw BadRequestException if request is not pending approval", async () => {
      requestRepository.findById.mockResolvedValue({ ...mockRequest, status: RequestStatus.APPROVED });
      await expect(service.handleAction("user1", "r1", REQUEST_ACTION.APPROVE)).rejects.toThrow(BadRequestException);
    });

    it("should throw BadRequestException for invalid action", async () => {
      await expect(service.handleAction("user1", "r1", "invalid_action" as any)).rejects.toThrow(BadRequestException);
    });
  });

  describe("revokeAccess", () => {
    it("should delete a request", async () => {
      await service.revokeAccess("user1", "r1");
      expect(requestRepository.delete).toHaveBeenCalledWith("r1");
    });

    it("should throw NotFoundException if request not found", async () => {
      requestRepository.findById.mockResolvedValue(null);
      await expect(service.revokeAccess("user1", "r2")).rejects.toThrow(NotFoundException);
    });
  });
});
