import { Injectable, UnprocessableEntityException } from "@nestjs/common";
import { SearchQueryBuilder } from "./search-query.builder";
import { Request, RequestStatus, Formulation } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

export type RequestWithRelations = Request & {
  requester: {
    id: string
    name: string
    email: string
    locationId: string
    departmentId: string
    roleId: string
    createdAt: Date
    updatedAt: Date
    location: {
      id: string
      city: string
      country: string
    }
    department: {
      id: string
      name: string
    }
  }
  formulation: Formulation
};

export interface RequestFilters {
  search?: string
  status?: RequestStatus
  page?: number
  limit?: number
}

@Injectable()
export class RequestRepository {
  public constructor(
    private readonly prisma: PrismaService,
    private readonly searchQueryBuilder: SearchQueryBuilder
  ) {}

  public async findAll(filters: RequestFilters) {
    const { search, status, page = 1, limit = 10 } = filters;
    const skip = (page - 1) * limit;

    const where: Record<string, unknown> = {};
    let requestIds: string[] = [];

    if (search) {
      const rawResults = await this.searchQueryBuilder.searchRequestWithRelations(search);
      requestIds = rawResults.map((r: { id: string }) => r.id);

      if (requestIds.length === 0) {
        return { data: [], limit, page, total: 0 };
      }

      where.id = { in: requestIds };
    }

    if (status) {
      where.status = status;
    }

    const [data, total] = await Promise.all([
      this.prisma.request.findMany({
        include: {
          formulation: true,
          requester: {
            include: {
              department: true,
              location: true,
            },
          },
        },
        orderBy: {
          id: "desc",
        },
        skip,
        take: limit,
        where,
      }),
      this.prisma.request.count({ where }),
    ]);

    return { data, limit, page, total };
  }

  public async create(requesterId: string, formulationId: string) {
    try {
      const request = await this.prisma.request.create({
        data: {
          formulationId,
          requesterId,
          status: RequestStatus.PENDING_APPROVAL,
        },
        include: {
          formulation: true,
          requester: {
            include: {
              department: true,
              location: true,
            },
          },
        },
      });

      return request;
    }
    catch (error: unknown) {
      if ((error as { code: string }).code === "P2002") {
        throw new UnprocessableEntityException("Your request has been sent. We will send you a notification when your request is approved.");
      }
      throw error;
    }
  }

  public async findById(id: string) {
    return this.prisma.request.findUnique({
      include: {
        formulation: true,
        requester: {
          include: {
            department: true,
            location: true,
          },
        },
      },
      where: { id },
    });
  }

  public async updateStatus(id: string, status: RequestStatus) {
    return this.prisma.request.update({
      data: { status },
      include: {
        formulation: true,
        requester: {
          include: {
            department: true,
            location: true,
          },
        },
      },
      where: { id },
    });
  }

  public async delete(id: string) {
    await this.prisma.request.delete({
      where: { id },
    });
  }
}
