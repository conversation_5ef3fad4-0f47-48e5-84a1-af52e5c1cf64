import { ApiProperty } from "@nestjs/swagger";
import { DepartmentDto } from "./department.dto";
import { LocationDto } from "./location.dto";

export class RequesterDto {
  @ApiProperty({
    description: "User ID",
    example: "48aa4462-0f83-4595-821e-d0dd03671d0f",
  })
  public id: string;

  @ApiProperty({
    description: "User name",
    example: "Roger Press",
  })
  public name: string;

  @ApiProperty({
    description: "User email",
    example: "<EMAIL>",
  })
  public email: string;

  @ApiProperty({
    description: "Location ID",
    example: "0ac08c3e-b21a-4e41-a671-6865fc0b4b9f",
  })
  public locationId: string;

  @ApiProperty({
    description: "Department ID",
    example: "21477dfd-f663-44a2-9213-510aac3fcdbf",
  })
  public departmentId: string;

  @ApiProperty({
    description: "Role ID",
    example: "cedd7124-a821-4fe2-9b4c-dd7768bc9a6a",
  })
  public roleId: string;

  @ApiProperty({
    description: "Creation timestamp",
    example: "2025-07-15T10:34:28.366Z",
  })
  public createdAt: string;

  @ApiProperty({
    description: "Last update timestamp",
    example: "2025-07-15T10:34:28.366Z",
  })
  public updatedAt: string;

  @ApiProperty({
    description: "User location information",
    type: LocationDto,
  })
  public location: LocationDto;

  @ApiProperty({
    description: "User department information",
    type: DepartmentDto,
  })
  public department: DepartmentDto;
}
