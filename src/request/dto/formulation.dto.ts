import { ApiProperty } from "@nestjs/swagger";

export class FormulationDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "dc8f29e2-e679-4b38-a1a4-41ef59a01a55",
  })
  public id: string;

  @ApiProperty({
    description: "Formulation name",
    example: "<PERSON>umper",
  })
  public name: string;

  @ApiProperty({
    description: "Formulation owner ID",
    example: "a68f285c-35ca-4ac4-901a-d8d80f7d910f",
  })
  public ownerId: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  public grade: string;
}
