import { ApiProperty } from "@nestjs/swagger";
import { FormulationDto } from "./formulation.dto";
import { RequesterDto } from "./requester.dto";
import { RequestStatus } from "@/generated/prisma";

export class RequestResponseDto {
  @ApiProperty({
    description: "Request ID",
    example: "c251f675-3623-4326-8cbc-34609c29641a",
  })
  public id: string;

  @ApiProperty({
    description: "Request status",
    enum: RequestStatus,
    example: "REJECTED",
  })
  public status: RequestStatus;

  @ApiProperty({
    description: "User who made the request",
    type: RequesterDto,
  })
  public requester: RequesterDto;

  @ApiProperty({
    description: "Formulation being requested",
    type: FormulationDto,
  })
  public formulation: FormulationDto;
}
