import { ApiProperty } from "@nestjs/swagger";

export class LocationDto {
  @ApiProperty({
    description: "Location ID",
    example: "0ac08c3e-b21a-4e41-a671-6865fc0b4b9f",
  })
  public id: string;

  @ApiProperty({
    description: "City name",
    example: "Paris",
  })
  public city: string;

  @ApiProperty({
    description: "Country name",
    example: "France",
  })
  public country: string;
}
