import { ConflictException, NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { UserRole } from "../role/role.types";
import {
  CreateUserDto,
  UpdateUserDto,
  UserFilterDto,
  UserResponseDto,
} from "./dto";
import { UserRepository, UserWithRelations } from "./repositories";
import { UserService } from "./user.service";
import { DepartmentService } from "@/department";
import { LocationService } from "@/location";
import { RoleService } from "@/role";

describe("UserService", () => {
  let service: UserService;
  let userRepository: UserRepository;
  let roleService: RoleService;
  let locationService: LocationService;
  let departmentService: DepartmentService;

  const mockUser: UserWithRelations = {
    department: {
      name: "Technology",
    },
    departmentId: "dept-456",
    email: "<EMAIL>",
    id: "user-123",
    location: {
      city: "Paris",
      country: "France",
    },
    locationId: "loc-123",
    name: "<PERSON>",
    role: {
      code: "ENGINEERS",
    },
    roleId: "role-789",
  };

  const mockUserResponse: UserResponseDto = {
    department: "Technology",
    email: "<EMAIL>",
    id: "user-123",
    location: "Paris, France",
    name: "John Doe",
    role: "ENGINEERS",
  };

  beforeEach(async () => {
    const mockUserRepository = {
      create: jest.fn(),
      delete: jest.fn(),
      findAll: jest.fn(),
      findByEmail: jest.fn(),
      findById: jest.fn(),
      findOneOrThrow: jest.fn(),
      update: jest.fn(),
    };

    const mockRoleService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      findOneOrThrow: jest.fn(),
    };

    const mockLocationService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      findOneOrThrow: jest.fn(),
    };

    const mockDepartmentService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      findOneOrThrow: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: UserRepository,
          useValue: mockUserRepository,
        },
        {
          provide: RoleService,
          useValue: mockRoleService,
        },
        {
          provide: LocationService,
          useValue: mockLocationService,
        },
        {
          provide: DepartmentService,
          useValue: mockDepartmentService,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    userRepository = module.get<UserRepository>(UserRepository);
    roleService = module.get<RoleService>(RoleService);
    locationService = module.get<LocationService>(LocationService);
    departmentService = module.get<DepartmentService>(DepartmentService);
  });

  describe("create", () => {
    it("should create a new user successfully", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      userRepository.findByEmail = jest.fn().mockResolvedValue(null);
      locationService.findOneOrThrow = jest.fn().mockResolvedValue({ id: "loc-123" });
      departmentService.findOneOrThrow = jest.fn().mockResolvedValue({ id: "dept-456" });
      roleService.findOneOrThrow = jest.fn().mockResolvedValue({ id: "role-789" });
      userRepository.create = jest.fn().mockResolvedValue(mockUser);

      const result = await service.create(createUserDto);

      expect(userRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(locationService.findOneOrThrow).toHaveBeenCalledWith("loc-123");
      expect(departmentService.findOneOrThrow).toHaveBeenCalledWith("dept-456");
      expect(roleService.findOneOrThrow).toHaveBeenCalledWith("role-789");
      expect(userRepository.create).toHaveBeenCalledWith({
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      });
      expect(result).toEqual(mockUserResponse);
    });

    it("should throw ConflictException when email already exists", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "loc-123",
        name: "John Doe",
        roleId: "role-789",
      };

      userRepository.findByEmail = jest.fn().mockResolvedValue(mockUser);

      await expect(service.create(createUserDto)).rejects.toThrow(ConflictException);
      expect(userRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should throw NotFoundException when location not found", async () => {
      const createUserDto: CreateUserDto = {
        departmentId: "dept-456",
        email: "<EMAIL>",
        locationId: "invalid-loc",
        name: "John Doe",
        roleId: "role-789",
      };

      userRepository.findByEmail = jest.fn().mockResolvedValue(null);
      locationService.findOneOrThrow = jest.fn().mockRejectedValue(new NotFoundException());

      await expect(service.create(createUserDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe("findAll", () => {
    it("should return paginated list of users", async () => {
      const filters: UserFilterDto = {
        limit: 10,
        page: 1,
        search: "john",
      };

      const mockRepositoryResult = {
        data: [mockUser],
        limit: 10,
        page: 1,
        total: 1,
      };

      userRepository.findAll = jest.fn().mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll(filters);

      expect(userRepository.findAll).toHaveBeenCalledWith({
        departmentId: undefined,
        limit: 10,
        locationId: undefined,
        page: 1,
        roleId: undefined,
        search: "john",
      });

      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual(mockUserResponse);
      expect(result.meta.total).toBe(1);
    });

    it("should handle empty results", async () => {
      const filters: UserFilterDto = {};

      const mockRepositoryResult = {
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      };

      userRepository.findAll = jest.fn().mockResolvedValue(mockRepositoryResult);

      const result = await service.findAll(filters);

      expect(result.data).toHaveLength(0);
      expect(result.meta.total).toBe(0);
    });

    it("should apply all filters correctly", async () => {
      const filters: UserFilterDto = {
        departmentId: "dept-456",
        limit: 5,
        locationId: "loc-123",
        page: 2,
        roleId: "role-789",
        search: "doe",
      };

      userRepository.findAll = jest.fn().mockResolvedValue({
        data: [],
        limit: 5,
        page: 2,
        total: 0,
      });

      await service.findAll(filters);

      expect(userRepository.findAll).toHaveBeenCalledWith({
        departmentId: "dept-456",
        limit: 5,
        locationId: "loc-123",
        page: 2,
        roleId: "role-789",
        search: "doe",
      });
    });

    it("should apply roleCode filter correctly", async () => {
      const filters: UserFilterDto = {
        roleCode: UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
      };

      userRepository.findAll = jest.fn().mockResolvedValue({
        data: [],
        limit: 10,
        page: 1,
        total: 0,
      });

      await service.findAll(filters);

      expect(userRepository.findAll).toHaveBeenCalledWith({
        departmentId: undefined,
        limit: 10,
        locationId: undefined,
        page: 1,
        roleCode: UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
        roleId: undefined,
        search: undefined,
      });
    });
  });

  describe("findOne", () => {
    it("should return user by ID", async () => {
      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);

      const result = await service.findOne("user-123");

      expect(userRepository.findOneOrThrow).toHaveBeenCalledWith("user-123");
      expect(result).toEqual(mockUserResponse);
    });

    it("should throw NotFoundException when user not found", async () => {
      userRepository.findOneOrThrow = jest.fn().mockRejectedValue(new Error("User not found"));

      await expect(service.findOne("non-existent")).rejects.toThrow(Error);
      expect(userRepository.findOneOrThrow).toHaveBeenCalledWith("non-existent");
    });
  });

  describe("update", () => {
    const updateUserDto: UpdateUserDto = {
      email: "<EMAIL>",
      name: "John Updated",
    };

    it("should update user successfully", async () => {
      const updatedUser = { ...mockUser, email: "<EMAIL>", name: "John Updated" };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      userRepository.findByEmail = jest.fn().mockResolvedValue(null);
      userRepository.update = jest.fn().mockResolvedValue(updatedUser);

      const result = await service.update("user-123", updateUserDto);

      expect(userRepository.findOneOrThrow).toHaveBeenCalledWith("user-123");
      expect(userRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
      expect(userRepository.update).toHaveBeenCalledWith("user-123", updateUserDto);
      expect(result.name).toBe("John Updated");
      expect(result.email).toBe("<EMAIL>");
    });

    it("should throw ConflictException when email already exists", async () => {
      const existingUser = { ...mockUser, id: "different-user" };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      userRepository.findByEmail = jest.fn().mockResolvedValue(existingUser);

      await expect(service.update("user-123", updateUserDto)).rejects.toThrow(ConflictException);
      expect(userRepository.findOneOrThrow).toHaveBeenCalledWith("user-123");
      expect(userRepository.findByEmail).toHaveBeenCalledWith("<EMAIL>");
    });

    it("should allow updating email to the same email", async () => {
      const sameEmailDto: UpdateUserDto = {
        email: "<EMAIL>",
        name: "John Updated",
      };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      userRepository.update = jest.fn().mockResolvedValue(mockUser);

      await service.update("user-123", sameEmailDto);

      expect(userRepository.findByEmail).not.toHaveBeenCalled();
      expect(userRepository.update).toHaveBeenCalledWith("user-123", sameEmailDto);
    });

    it("should update with location change", async () => {
      const updateWithLocation: UpdateUserDto = {
        locationId: "new-loc-456",
      };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      locationService.findOne = jest.fn().mockResolvedValue({ id: "new-loc-456" });
      userRepository.update = jest.fn().mockResolvedValue(mockUser);

      await service.update("user-123", updateWithLocation);

      expect(locationService.findOne).toHaveBeenCalledWith("new-loc-456");
      expect(userRepository.update).toHaveBeenCalledWith("user-123", {
        email: undefined,
        locationId: "new-loc-456",
        name: undefined,
      });
    });

    it("should throw NotFoundException when location not found", async () => {
      const updateWithInvalidLocation: UpdateUserDto = {
        locationId: "invalid-loc",
      };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      locationService.findOne = jest.fn().mockResolvedValue(null);

      await expect(service.update("user-123", updateWithInvalidLocation)).rejects.toThrow(
        NotFoundException,
      );
      expect(locationService.findOne).toHaveBeenCalledWith("invalid-loc");
    });

    it("should update with department change", async () => {
      const updateWithDepartment: UpdateUserDto = {
        departmentId: "new-dept-456",
      };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      departmentService.findOne = jest.fn().mockResolvedValue({ id: "new-dept-456" });
      userRepository.update = jest.fn().mockResolvedValue(mockUser);

      await service.update("user-123", updateWithDepartment);

      expect(departmentService.findOne).toHaveBeenCalledWith("new-dept-456");
      expect(userRepository.update).toHaveBeenCalledWith("user-123", {
        departmentId: "new-dept-456",
        email: undefined,
        name: undefined,
      });
    });

    it("should update with role change", async () => {
      const updateWithRole: UpdateUserDto = {
        roleId: "new-role-456",
      };

      userRepository.findOneOrThrow = jest.fn().mockResolvedValue(mockUser);
      roleService.findOne = jest.fn().mockResolvedValue({ id: "new-role-456" });
      userRepository.update = jest.fn().mockResolvedValue(mockUser);

      await service.update("user-123", updateWithRole);

      expect(roleService.findOne).toHaveBeenCalledWith("new-role-456");
      expect(userRepository.update).toHaveBeenCalledWith("user-123", {
        email: undefined,
        name: undefined,
        roleId: "new-role-456",
      });
    });
  });

  describe("remove", () => {
    it("should delete user successfully", async () => {
      userRepository.findById = jest.fn().mockResolvedValue(mockUser);
      userRepository.delete = jest.fn();

      await service.remove("user-123");

      expect(userRepository.findById).toHaveBeenCalledWith("user-123");
      expect(userRepository.delete).toHaveBeenCalledWith("user-123");
    });

    it("should throw NotFoundException when user not found", async () => {
      userRepository.findById = jest.fn().mockResolvedValue(null);

      await expect(service.remove("non-existent")).rejects.toThrow(NotFoundException);
      expect(userRepository.findById).toHaveBeenCalledWith("non-existent");
      expect(userRepository.delete).not.toHaveBeenCalled();
    });
  });

  describe("getFilterOptions", () => {
    it("should return filter options for dropdowns", async () => {
      const mockRoles = [{ id: "role-1", name: "Engineers" }];
      const mockLocations = [{ city: "Paris", country: "France", id: "loc-1" }];
      const mockDepartments = [{ id: "dept-1", name: "Technology" }];

      roleService.findAll = jest.fn().mockResolvedValue(mockRoles);
      locationService.findAll = jest.fn().mockResolvedValue(mockLocations);
      departmentService.findAll = jest.fn().mockResolvedValue(mockDepartments);

      const result = await service.getFilterOptions();

      expect(roleService.findAll).toHaveBeenCalled();
      expect(locationService.findAll).toHaveBeenCalled();
      expect(departmentService.findAll).toHaveBeenCalled();

      expect(result).toEqual({
        departments: [{ id: "dept-1", name: "Technology" }],
        locations: [{ id: "loc-1", name: "Paris, France" }],
        roles: [{ id: "role-1", name: "Engineers" }],
      });
    });
  });
});
