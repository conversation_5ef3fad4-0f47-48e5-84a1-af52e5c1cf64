import { ConflictException, Injectable, NotFoundException } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils";
import { CreateUserDto, PaginatedUserResponseDto, UpdateUserDto, UserFilterDto, UserResponseDto } from "./dto";
import { UpdateUserData, UserRepository, UserWithRelations } from "./repositories";
import { DepartmentService } from "@/department";
import { LocationService } from "@/location";
import { RoleService } from "@/role";

@Injectable()
export class UserService {
  public constructor(
    private readonly userRepository: UserRepository,
    private readonly roleService: RoleService,
    private readonly locationService: LocationService,
    private readonly departmentService: DepartmentService,
  ) {}

  public async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    const existingUser = await this.userRepository.findByEmail(createUserDto.email);

    if (existingUser) {
      throw new ConflictException("User with this email already exists");
    }

    await Promise.all([
      this.locationService.findOneOrThrow(createUserDto.locationId),
      this.departmentService.findOneOrThrow(createUserDto.departmentId),
      this.roleService.findOneOrThrow(createUserDto.roleId),
    ]);

    const user = await this.userRepository.create({
      departmentId: createUserDto.departmentId,
      email: createUserDto.email,
      locationId: createUserDto.locationId,
      name: createUserDto.name,
      roleId: createUserDto.roleId,
    });

    return this.mapToUserResponse(user);
  }

  public async findAll(filters: UserFilterDto): Promise<PaginatedUserResponseDto> {
    const { page, limit } = normalizePaginationParameters(filters.page, filters.limit);

    const result = await this.userRepository.findAll({
      departmentId: filters.departmentId,
      limit,
      locationId: filters.locationId,
      page,
      roleCode: filters.roleCode,
      roleId: filters.roleId,
      search: filters.search,
    });

    const mappedData = result.data.map(user => this.mapToUserResponse(user));

    return createPaginatedResponse({
      data: mappedData,
      limit: result.limit,
      page: result.page,
      total: result.total,
    });
  }

  public async findOne(id: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOneOrThrow(id);
    return this.mapToUserResponse(user);
  }

  public async update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    const existingUser = await this.userRepository.findOneOrThrow(id);
    if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
      const emailExists = await this.userRepository.findByEmail(updateUserDto.email);

      if (emailExists) {
        throw new ConflictException("User with this email already exists");
      }
    }

    const updateData: UpdateUserData = {
      email: updateUserDto.email,
      name: updateUserDto.name,
    };

    if (updateUserDto.locationId) {
      const location = await this.locationService.findOne(updateUserDto.locationId);
      if (!location) {
        throw new NotFoundException(`Location with ID '${updateUserDto.locationId}' not found`);
      }
      updateData.locationId = updateUserDto.locationId;
    }

    if (updateUserDto.departmentId) {
      const department = await this.departmentService.findOne(updateUserDto.departmentId);
      if (!department) {
        throw new NotFoundException(`Department with ID '${updateUserDto.departmentId}' not found`);
      }
      updateData.departmentId = updateUserDto.departmentId;
    }

    if (updateUserDto.roleId) {
      const role = await this.roleService.findOne(updateUserDto.roleId);
      if (!role) {
        throw new NotFoundException(`Role with ID '${updateUserDto.roleId}' not found`);
      }
      updateData.roleId = updateUserDto.roleId;
    }

    const user = await this.userRepository.update(id, updateData);

    return this.mapToUserResponse(user);
  }

  public async remove(id: string): Promise<void> {
    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new NotFoundException(`User with ID '${id}' not found`);
    }
    await this.userRepository.delete(id);
  }

  public async getFilterOptions() {
    const [roles, locations, departments] = await Promise.all([
      this.roleService.findAll(),
      this.locationService.findAll(),
      this.departmentService.findAll(),
    ]);

    return {
      departments: departments.map(department => ({
        id: department.id,
        name: department.name,
      })),
      locations: locations.map(location => ({
        id: location.id,
        name: `${location.city}, ${location.country}`,
      })),
      roles: roles.map(role => ({ id: role.id, name: role.name })),
    };
  }

  private mapToUserResponse(user: UserWithRelations): UserResponseDto {
    return {
      department: user.department.name,
      email: user.email,
      id: user.id,
      location: `${user.location.city}, ${user.location.country}`,
      name: user.name,
      role: user.role.code,
    };
  }
}
