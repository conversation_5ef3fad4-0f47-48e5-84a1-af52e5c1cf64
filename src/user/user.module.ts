import { <PERSON>du<PERSON> } from "@nestjs/common";
import { DepartmentModule } from "../department/department.module";
import { LocationModule } from "../location/location.module";
import { PrismaService } from "../prisma.service";
import { RoleModule } from "../role/role.module";
import { UserRepository } from "./repositories/user.repository";
import { UserController } from "./user.controller";
import { UserService } from "./user.service";
import { AuthModule } from "@/auth/auth.module";

@Module({
  controllers: [UserController],
  exports: [UserService, UserRepository],
  imports: [AuthModule, RoleModule, LocationModule, DepartmentModule],
  providers: [UserService, UserRepository, PrismaService],
})
export class UserModule {}
