import { Injectable } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils";
import { PaginatedSpecificationResponseDto, SpecificationResponseDto } from "./dto";
import { SpecificationRepository } from "./repositories/specification.repository";
import type { Specification } from "@/generated/prisma";

@Injectable()
export class SpecificationService {
  public constructor(
    private readonly specificationRepository: SpecificationRepository,
  ) {}

  public async findAll(filters: {
    search?: string
    page?: number
    limit?: number
  }): Promise<PaginatedSpecificationResponseDto> {
    const { page, limit } = normalizePaginationParameters(filters.page, filters.limit);

    const result = await this.specificationRepository.findAll({
      search: filters.search,
      page,
      limit,
    });

    return createPaginatedResponse({
      data: result.data.map(spec => this.mapToResponseDto(spec)),
      total: result.total,
      page: result.page,
      limit: result.limit,
    });
  }

  private mapToResponseDto(specification: Specification): SpecificationResponseDto {
    return {
      id: specification.id,
      property: specification.property,
      label: specification.label,
      unit: specification.unit,
      type: specification.type,
      createdAt: specification.createdAt,
      updatedAt: specification.updatedAt,
    };
  }
}
