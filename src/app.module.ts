import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { AuthService } from "./auth/auth.service";
import { DepartmentModule } from "./department/department.module";
import { FormulationModule } from "./formulation/formulation.module";
import { LocationModule } from "./location/location.module";
import { MaterialModule } from "./material/material.module";
import { NotificationModule } from "./notification/notification.module";
import { PrismaService } from "./prisma.service";
import { RequestModule } from "./request/request.module";
import { RoleModule } from "./role/role.module";
import { SpecificationModule } from "./specification/specification.module";
import { SupplierModule } from "./supplier/supplier.module";
import { UserModule } from "./user/user.module";

@Module({
  controllers: [AppController],
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    EventEmitterModule.forRoot(),
    RoleModule,
    LocationModule,
    DepartmentModule,
    UserModule,
    MaterialModule,
    SpecificationModule,
    FormulationModule,
    NotificationModule,
    RequestModule,
    SupplierModule,
  ],
  providers: [AppService, AuthService, PrismaService],
})
export class AppModule {}
