import {
  <PERSON>,
  Get,
  Query,
  HttpStatus,
  HttpCode,
  UseGuards,
  Req,
  Patch,
  UnauthorizedException,
  Body,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOAuth2,
  <PERSON>pi<PERSON><PERSON><PERSON>,
} from "@nestjs/swagger";
import { NotificationQueryDto, NotificationResponseDto, PaginatedNotificationResponseDto } from "./dto";
import { MarkAsReadDto } from "./dto/mark-as-read.dto";
import { NotificationService } from "./notification.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard, ROLES } from "@/auth/role.guard";
import { ErrorResponseDto } from "@/common/dto";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("notifications")
@Controller("notifications")
export class NotificationController {
  public constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ROLES()
  @ApiOperation({
    description: "Retrieve notifications for the authenticated user with optional filtering by type and read status. Response includes unreadCount for all unread notifications. Available to all authenticated users.",
    summary: "Get user notifications",
  })
  @ApiQuery({
    description: "Page number",
    example: 1,
    name: "page",
    required: false,
  })
  @ApiQuery({
    description: "Number of items per page",
    example: 10,
    name: "limit",
    required: false,
  })
  @ApiQuery({
    description: "Filter by notification type",
    enum: ["MATERIAL_UPDATE", "REQUEST_UPDATE", "FORMULATION_UPDATE", "SYSTEM_ANNOUNCEMENT"],
    name: "type",
    required: false,
  })
  @ApiQuery({
    description: "Filter by read status",
    name: "isRead",
    required: false,
    type: Boolean,
  })
  @ApiResponse({
    description: "Notifications retrieved successfully with unread count",
    status: HttpStatus.OK,
    type: PaginatedNotificationResponseDto,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
    type: ErrorResponseDto,
  })
  @ApiResponse({
    description: "Forbidden - User not authenticated",
    status: HttpStatus.FORBIDDEN,
    type: ErrorResponseDto,
  })
  public async getNotifications(
    @Query() query: NotificationQueryDto,
    @Req() request: Record<string, unknown>,
  ): Promise<PaginatedNotificationResponseDto> {
    const userId = (request.user as Record<string, unknown>)?.oid ?? (request.headers as Record<string, unknown>)?.oid;

    if (!userId) {
      throw new UnauthorizedException("User ID not found in request");
    }

    const notifications = await this.notificationService.getNotifications(userId as string, query);

    const ids = notifications.data.map(n => n.id);
    if (ids.length > 0) {
      await this.notificationService.markAsSeen(ids, userId as string);

      for (const n of notifications.data) {
        n.isSeen = true;
      }
    }
    return notifications;
  }

  @Patch("mark-as-read")
  @HttpCode(HttpStatus.OK)
  @ROLES()
  @ApiOperation({
    description: "Mark multiple notifications as read. Available to all authenticated users.",
    summary: "Mark notifications as read",
  })
  @ApiResponse({
    description: "Notifications marked as read successfully",
    status: HttpStatus.OK,
    type: [NotificationResponseDto],
  })
  @ApiResponse({
    description: "One or more notifications not found",
    status: HttpStatus.NOT_FOUND,
    type: ErrorResponseDto,
  })
  public async markAsRead(
    @Req() request: Record<string, unknown>,
    @Body() body: MarkAsReadDto,
  ): Promise<NotificationResponseDto[]> {
    const userId = (request.user as Record<string, unknown>)?.oid ?? (request.headers as Record<string, unknown>)?.oid;
    if (!userId) {
      throw new UnauthorizedException("User ID not found in request");
    }
    return this.notificationService.markManyAsRead(body.ids, userId as string);
  }
}
