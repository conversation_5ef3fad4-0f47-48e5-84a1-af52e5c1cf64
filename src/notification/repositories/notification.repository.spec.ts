import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { NotificationRepository } from "./notification.repository";
import { NotificationType } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

describe("NotificationRepository", () => {
  let repository: NotificationRepository;

  const mockPrismaService = {
    notification: {
      count: jest.fn(),
      createMany: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
    },
  };

  const mockNotification = {
    createdAt: new Date("2024-01-01T00:00:00Z"),
    id: "notification-1",
    isRead: false,
    isSeen: false,
    message: "This is a test notification",
    readAt: undefined,
    title: "Test Notification",
    type: NotificationType.MATERIAL_UPDATE,
    updatedAt: new Date("2024-01-01T00:00:00Z"),
    userId: "user-1",
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<NotificationRepository>(NotificationRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(repository).toBeDefined();
  });

  describe("findAll", () => {
    it("should find all notifications with filters", async () => {
      const mockData = [mockNotification];
      mockPrismaService.notification.findMany.mockResolvedValue(mockData);
      mockPrismaService.notification.count.mockResolvedValue(1);

      const filters = {
        isRead: false,
        limit: 10,
        page: 1,
        type: NotificationType.MATERIAL_UPDATE,
        userId: "user-1",
      };

      const result = await repository.findAll(filters);

      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: "desc" },
        skip: 0,
        take: 10,
        where: {
          isRead: false,
          type: NotificationType.MATERIAL_UPDATE,
          userId: "user-1",
        },
      });
      expect(mockPrismaService.notification.count).toHaveBeenCalledWith({
        where: {
          isRead: false,
          type: NotificationType.MATERIAL_UPDATE,
          userId: "user-1",
        },
      });
      expect(result).toEqual({
        data: mockData,
        limit: 10,
        page: 1,
        total: 1,
      });
    });

    it("should handle pagination correctly", async () => {
      mockPrismaService.notification.findMany.mockResolvedValue([]);
      mockPrismaService.notification.count.mockResolvedValue(25);

      const filters = {
        limit: 5,
        page: 3,
        userId: "user-1",
      };

      await repository.findAll(filters);

      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: "desc" },
        skip: 10,
        take: 5,
        where: { userId: "user-1" },
      });
    });

    it("should use default pagination when not provided", async () => {
      mockPrismaService.notification.findMany.mockResolvedValue([]);
      mockPrismaService.notification.count.mockResolvedValue(0);

      const filters = { userId: "user-1" };

      await repository.findAll(filters);

      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: "desc" },
        skip: 0,
        take: 10,
        where: { userId: "user-1" },
      });
    });
  });

  describe("findById", () => {
    it("should find notification by id", async () => {
      mockPrismaService.notification.findUnique.mockResolvedValue(mockNotification);

      const result = await repository.findById("notification-1");

      expect(mockPrismaService.notification.findUnique).toHaveBeenCalledWith({
        where: { id: "notification-1" },
      });
      expect(result).toEqual(mockNotification);
    });

    it("should return null when notification not found", async () => {
      mockPrismaService.notification.findUnique.mockImplementation(() => Promise.resolve());

      const result = await repository.findById("non-existent");

      expect(result).toBeUndefined();
    });
  });

  describe("findOneOrThrow", () => {
    it("should return notification when found", async () => {
      mockPrismaService.notification.findUnique.mockResolvedValue(mockNotification);

      const result = await repository.findOneOrThrow("notification-1");

      expect(result).toEqual(mockNotification);
    });

    it("should throw NotFoundException when notification not found", async () => {
      mockPrismaService.notification.findUnique.mockImplementation(() => Promise.resolve());

      await expect(repository.findOneOrThrow("non-existent")).rejects.toThrow(
        new NotFoundException("Notification with ID 'non-existent' not found")
      );
    });
  });

  describe("markAsRead", () => {
    it("should mark notification as read", async () => {
      const readNotification = { ...mockNotification, isRead: true, readAt: new Date() };
      mockPrismaService.notification.update.mockResolvedValue(readNotification);

      const result = await repository.markAsRead("notification-1");

      expect(mockPrismaService.notification.update).toHaveBeenCalledWith({
        data: {
          isRead: true,
          readAt: expect.any(Date) as Date,
        },
        where: { id: "notification-1" },
      });
      expect(result.isRead).toBe(true);
    });
  });

  describe("markAsSeen", () => {
    it("should mark multiple notifications as seen", async () => {
      const notificationIds = ["notification-1", "notification-2"];
      const userId = "user-1";

      await repository.markAsSeen(notificationIds, userId);

      expect(mockPrismaService.notification.updateMany).toHaveBeenCalledWith({
        data: {
          isSeen: true,
        },
        where: {
          id: { in: notificationIds },
          isSeen: false,
          userId,
        },
      });
    });
  });

  describe("markManyAsRead", () => {
    it("should mark multiple notifications as read", async () => {
      const notificationIds = ["notification-1", "notification-2"];
      const userId = "user-1";
      const readNotifications = [
        { ...mockNotification, isRead: true },
        { ...mockNotification, id: "notification-2", isRead: true },
      ];

      mockPrismaService.notification.findMany.mockResolvedValue(readNotifications);

      const result = await repository.markManyAsRead(notificationIds, userId);

      expect(mockPrismaService.notification.updateMany).toHaveBeenCalledWith({
        data: {
          isRead: true,
          readAt: expect.any(Date) as Date,
        },
        where: {
          id: { in: notificationIds },
          isRead: false,
          userId,
        },
      });
      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        where: {
          id: { in: notificationIds },
          userId,
        },
      });
      expect(result).toEqual(readNotifications);
    });
  });

  describe("getUnreadCount", () => {
    it("should return unread notification count for user", async () => {
      mockPrismaService.notification.count.mockResolvedValue(5);

      const result = await repository.getUnreadCount("user-1");

      expect(mockPrismaService.notification.count).toHaveBeenCalledWith({
        where: {
          isRead: false,
          userId: "user-1",
        },
      });
      expect(result).toBe(5);
    });

    it("should return 0 when no unread notifications", async () => {
      mockPrismaService.notification.count.mockResolvedValue(0);

      const result = await repository.getUnreadCount("user-1");

      expect(result).toBe(0);
    });
  });

  describe("createMany", () => {
    it("should create notifications for multiple users", async () => {
      const userIds = ["user-1", "user-2"];
      const title = "New Material Available";
      const message = "A new material has been added to the system";
      const type = NotificationType.MATERIAL_UPDATE;

      const createdNotifications = [
        { ...mockNotification, message, title, type, userId: "user-1" },
        { ...mockNotification, id: "notification-2", message, title, type, userId: "user-2" },
      ];

      mockPrismaService.notification.findMany.mockResolvedValue(createdNotifications);

      const result = await repository.createMany({ message, title, type, userIds });

      expect(mockPrismaService.notification.createMany).toHaveBeenCalledWith({
        data: [
          { message, title, type, userId: "user-1" },
          { message, title, type, userId: "user-2" },
        ],
      });
      expect(mockPrismaService.notification.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: "desc" },
        take: 2,
        where: {
          message,
          title,
          type,
          userId: { in: userIds },
        },
      });
      expect(result).toEqual(createdNotifications);
    });
  });
});
