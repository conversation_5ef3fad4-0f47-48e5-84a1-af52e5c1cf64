import { UnauthorizedException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { NotificationResponseDto } from "./dto";
import { NotificationController } from "./notification.controller";
import { NotificationService } from "./notification.service";
import { AuthGuard } from "@/auth/auth.guard";
import { RoleGuard } from "@/auth/role.guard";
import { PaginatedResponseDto } from "@/common/dto";

describe("NotificationController", () => {
  let controller: NotificationController;
  let service: NotificationService;

  const mockNotificationResponse: NotificationResponseDto = {
    createdAt: new Date(),
    id: "1",
    isRead: false,
    isSeen: false,
    message: "Test",
    readAt: null,
    title: "Test",
    type: "MATERIAL_UPDATE",
    updatedAt: new Date(),
  };

  const meta = {
    currentPage: 1,
    from: 1,
    lastPage: 1,
    perPage: 10,
    to: 1,
    total: 1,
  };

  const paginatedResponse = new PaginatedResponseDto([mockNotificationResponse], meta);

  const mockNotificationService = {
    getNotifications: jest.fn().mockResolvedValue({ ...paginatedResponse, unreadCount: 1 }),
    markAsSeen: jest.fn(),
    markManyAsRead: jest.fn().mockResolvedValue([mockNotificationResponse]),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationController],
      providers: [
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
      ],
    })
      .overrideGuard(AuthGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(RoleGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<NotificationController>(NotificationController);
    service = module.get<NotificationService>(NotificationService);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });

  describe("getNotifications", () => {
    it("should return paginated notifications", async () => {
      const request = { user: { oid: "user1" } };
      const query = {};
      const result = await controller.getNotifications(query, request);
      expect(service.getNotifications).toHaveBeenCalledWith("user1", query);
      expect(service.markAsSeen).toHaveBeenCalledWith(["1"], "user1");
      expect(result.data[0].isSeen).toBe(true);
    });

    it("should throw UnauthorizedException if user ID is not found", async () => {
      const request = { user: {} };
      const query = {};
      await expect(controller.getNotifications(query, request)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe("markAsRead", () => {
    it("should mark notifications as read", async () => {
      const request = { user: { oid: "user1" } };
      const body = { ids: ["1"] };
      await controller.markAsRead(request, body);
      expect(service.markManyAsRead).toHaveBeenCalledWith(["1"], "user1");
    });

    it("should throw UnauthorizedException if user ID is not found", async () => {
      const request = { user: {} };
      const body = { ids: ["1"] };
      await expect(controller.markAsRead(request, body)).rejects.toThrow(UnauthorizedException);
    });
  });
});
