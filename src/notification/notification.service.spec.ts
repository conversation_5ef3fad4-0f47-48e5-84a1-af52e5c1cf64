import { NotFoundException } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { MaterialStatusChangedEvent } from "../material/events/material-status-changed.event";
import { NewFormulationRequestEvent } from "../request/events/new-formulation-request.event";
import { RequestStatusChangedEvent } from "../request/events/request-status-changed.event";
import { PaginatedNotificationResponseDto } from "./dto";
import { NotificationService } from "./notification.service";
import { NotificationRepository } from "./repositories";
import { MaterialStatus, NotificationType } from "@/generated/prisma";

describe("NotificationService", () => {
  let service: NotificationService;

  const mockNotification = {
    createdAt: new Date(),
    id: "1",
    isRead: false,
    isSeen: false,
    message: "Test Message",
    readAt: new Date(),
    title: "Test Notification",
    type: NotificationType.MATERIAL_UPDATE,
    updatedAt: new Date(),
  };

  let mockCreateMany: jest.Mock;
  let mockFindAll: jest.Mock;
  let mockFindOneOrThrow: jest.Mock;
  let mockGetUnreadCount: jest.Mock;
  let mockMarkAsRead: jest.Mock;
  let mockMarkAsSeen: jest.Mock;
  let mockMarkManyAsRead: jest.Mock;

  beforeEach(async () => {
    mockCreateMany = jest.fn().mockResolvedValue([mockNotification]);
    mockFindAll = jest.fn().mockResolvedValue({ data: [mockNotification], total: 1 });
    mockFindOneOrThrow = jest.fn().mockResolvedValue(mockNotification);
    mockGetUnreadCount = jest.fn().mockResolvedValue(1);
    mockMarkAsRead = jest.fn().mockResolvedValue({ ...mockNotification, isRead: true });
    mockMarkAsSeen = jest.fn().mockResolvedValue({});
    mockMarkManyAsRead = jest.fn().mockResolvedValue([{ ...mockNotification, isRead: true }]);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: NotificationRepository,
          useValue: {
            createMany: mockCreateMany,
            findAll: mockFindAll,
            findOneOrThrow: mockFindOneOrThrow,
            getUnreadCount: mockGetUnreadCount,
            markAsRead: mockMarkAsRead,
            markAsSeen: mockMarkAsSeen,
            markManyAsRead: mockMarkManyAsRead,
          },
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("createNotification", () => {
    it("should create and return notifications", async () => {
      const result = await service.createNotification({
        message: "Test",
        title: "Test",
        type: NotificationType.MATERIAL_UPDATE,
        userIds: ["user1"],
      });
      expect(mockCreateMany).toHaveBeenCalled();
      expect(result).toEqual([service["mapToNotificationResponse"](mockNotification)]);
    });
  });

  describe("getNotifications", () => {
    it("should return paginated notifications", async () => {
      const result: PaginatedNotificationResponseDto = await service.getNotifications("user1", {});
      expect(mockFindAll).toHaveBeenCalled();
      expect(mockGetUnreadCount).toHaveBeenCalled();
      expect(result.data).toEqual([service["mapToNotificationResponse"](mockNotification)]);
      expect(result.meta.total).toBe(1);
      expect(result.unreadCount).toBe(1);
    });
  });

  describe("markAsRead", () => {
    it("should mark a notification as read", async () => {
      const result = await service.markAsRead("1");
      expect(mockFindOneOrThrow).toHaveBeenCalledWith("1");
      expect(mockMarkAsRead).toHaveBeenCalledWith("1");
      expect(result.isRead).toBe(true);
    });

    it("should throw a NotFoundException if the notification is not found", async () => {
      mockFindOneOrThrow.mockRejectedValue(new NotFoundException());
      await expect(service.markAsRead("2")).rejects.toThrow(NotFoundException);
    });
  });

  describe("markAsSeen", () => {
    it("should mark notifications as seen", async () => {
      await service.markAsSeen(["1"], "user1");
      expect(mockMarkAsSeen).toHaveBeenCalledWith(["1"], "user1");
    });
  });

  describe("markManyAsRead", () => {
    it("should mark many notifications as read", async () => {
      const result = await service.markManyAsRead(["1"], "user1");
      expect(mockMarkManyAsRead).toHaveBeenCalledWith(["1"], "user1");
      expect(result[0].isRead).toBe(true);
    });
  });

  it("should handle NewFormulationRequestEvent and call createNotification", async () => {
    const spy = jest.spyOn(service, "createNotification");
    const event = new NewFormulationRequestEvent(
      "FormA",
      ["<EMAIL>"],
      ["manager1"],
      "user1",
      "Test message",
      "<html><body>Test</body></html>"
    );
    await service.handleNewFormulationNotification(event);
    expect(spy).toHaveBeenCalledWith({
      message: "Test message",
      title: "New Formulation Request",
      type: NotificationType.REQUEST_UPDATE,
      userIds: ["manager1"],
    });
  });

  it("should handle RequestStatusChangedEvent and call createNotification", async () => {
    const spy = jest.spyOn(service, "createNotification");
    const event = new RequestStatusChangedEvent(
      "user1",
      "FormA",
      "APPROVED"
    );
    await service.handleRequestStatusChangedNotification(event);
    expect(spy).toHaveBeenCalledWith({
      message: "Your request for formulation 'FormA' has been approved.",
      title: "Request Approved",
      type: NotificationType.REQUEST_UPDATE,
      userIds: ["user1"],
    });
  });

  describe("handleMaterialStatusChangedNotification", () => {
    it("should handle MaterialStatusChangedEvent with AVAILABLE status", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        MaterialStatus.AVAILABLE,
        ["user1", "user2"],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).toHaveBeenCalledWith({
        message: "Virgin PP Homopolymer has been characterized and validated by Feedstock & Recycling Engineering team and is available for compare and formulation request. Please check stock on Synergy.",
        title: "Material Available",
        type: NotificationType.MATERIAL_UPDATE,
        userIds: ["user1", "user2"],
      });
    });

    it("should handle MaterialStatusChangedEvent with ARCHIVE status", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        MaterialStatus.ARCHIVE,
        ["user1"],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).toHaveBeenCalledWith({
        message: "Virgin PP Homopolymer is not available for compare and formulation request. It could be a change of strategy/process internally or at the supplier. If you want to have more information, please ask the Feedstock Team",
        title: "Material Archived",
        type: NotificationType.MATERIAL_UPDATE,
        userIds: ["user1"],
      });
    });

    it("should handle MaterialStatusChangedEvent with UNDER_REVIEW status", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        MaterialStatus.UNDER_REVIEW,
        ["user1", "user2", "user3"],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).toHaveBeenCalledWith({
        message: "Virgin PP Homopolymer has been characterized and will be validated soon by Feedstock & Recycling Engineering team.",
        title: "Material Under Review",
        type: NotificationType.MATERIAL_UPDATE,
        userIds: ["user1", "user2", "user3"],
      });
    });

    it("should not create notification when newStatus is undefined", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        undefined as unknown as MaterialStatus,
        ["user1"],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).not.toHaveBeenCalled();
    });

    it("should not create notification when userIds is empty", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        MaterialStatus.AVAILABLE,
        [],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).not.toHaveBeenCalled();
    });

    it("should not create notification when userIds is undefined", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        MaterialStatus.AVAILABLE,
        undefined as unknown as string[],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).not.toHaveBeenCalled();
    });

    it("should not create notification for invalid material status", async () => {
      const spy = jest.spyOn(service, "createNotification");
      const event = new MaterialStatusChangedEvent(
        "INVALID_STATUS" as unknown as MaterialStatus,
        ["user1"],
        "Virgin PP Homopolymer"
      );

      await service.handleMaterialStatusChangedNotification(event);

      expect(spy).not.toHaveBeenCalled();
    });
  });
});
