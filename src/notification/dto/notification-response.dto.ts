import { ApiProperty } from "@nestjs/swagger";
import { NotificationType } from "@/generated/prisma";

export class NotificationResponseDto {
  @ApiProperty({ example: "123e4567-e89b-12d3-a456-426614174000" })
  public id: string;

  @ApiProperty({ example: "[Material Updates]" })
  public title: string;

  @ApiProperty({
    example: "PP-H 1200 has been characterized and validated by Feedstock & Recycling Engineering team and is available for compare and formulation request.",
  })
  public message: string;

  @ApiProperty({ enum: NotificationType, example: "MATERIAL_UPDATE" })
  public type: NotificationType;

  @ApiProperty({ example: false })
  public isRead: boolean;

  @ApiProperty({ example: false })
  public isSeen: boolean;

  @ApiProperty({ example: "2024-01-15T10:30:00Z", nullable: true })
  public readAt: Date | null;

  @ApiProperty({ example: "2024-01-15T09:00:00Z" })
  public createdAt: Date;

  @ApiProperty({ example: "2024-01-15T09:00:00Z" })
  public updatedAt: Date;
}
