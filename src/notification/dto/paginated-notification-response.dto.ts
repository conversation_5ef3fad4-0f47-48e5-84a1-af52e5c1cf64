import { ApiProperty } from "@nestjs/swagger";
import { NotificationResponseDto } from "./notification-response.dto";
import { PaginatedResponseDto } from "@/common/dto";

export class PaginatedNotificationResponseDto extends PaginatedResponseDto<NotificationResponseDto> {
  @ApiProperty({ type: [NotificationResponseDto] })
  public declare data: NotificationResponseDto[];

  @ApiProperty({ description: "Total count of unread notifications for the user", example: 5 })
  public unreadCount: number;
}
