import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
} from "@nestjs/common";
import {
  ApiBearerAuth, ApiBody, ApiOAuth2,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { format } from "date-fns";
import { Response } from "express";
import { AuthGuard } from "../auth/auth.guard";
import { RoleGuard, ROLES } from "../auth/role.guard";
import { AuthenticatedRequest } from "../common";
import { UserRole } from "../role/role.types";
import {
  BatchUpdateMaterialRequestDto,
  BatchUpdateMaterialResponseDto,
  MaterialComparisonRequestDto,
  MaterialComparisonResponseDto,
  MaterialFilterRequestDto,
  MaterialFilterResponseDto,
  MaterialSearchPageRequestDto,
  MaterialSearchRequestDto,
  MaterialSearchResponseDto,
  PaginatedMaterialPageResponseDto,
  PaginatedMaterialResponseDto,
  UpdateMaterialDto,
} from "./dto";
import { MaterialExportRequestDto } from "./dto/material-export-request.dto";
import { MaterialComparisonService } from "./material-comparison.service";
import { MaterialExportService } from "./material-export.service";
import { MaterialService } from "./material.service";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("materials")
@Controller("materials")
export class MaterialController {
  public constructor(
    private readonly materialService: MaterialService,
    private readonly materialComparisonService: MaterialComparisonService,
    private readonly materialExportService: MaterialExportService,
  ) {}

  @Get("")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Search materials with multiple filter options including family, type, status, supplier, and more. Engineers can only see available materials.",
    summary: "Search materials with comprehensive filtering",
  })
  @ApiResponse({
    description: "Materials matching criteria retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedMaterialResponseDto,
  })
  public async getMaterials(
    @Query() searchRequest: MaterialSearchRequestDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<PaginatedMaterialResponseDto> {
    const userRole = request?.user?.role;
    return this.materialService.searchMaterials({ ...searchRequest, userRole });
  }

  @Get("origins")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description: "Retrieve a list of all distinct material origins available in the system.",
    summary: "Get distinct material origins",
  })
  @ApiResponse({
    description: "Distinct material origins retrieved successfully",
    status: HttpStatus.OK,
    type: [String],
  })
  public async getMaterialOrigins(): Promise<string[]> {
    return this.materialService.getMaterialOrigins();
  }

  @Get("filters")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description: "Get filtered values for a specific column (origin, status, type) with pagination and search support.",
    summary: "Get material column filter values",
  })
  @ApiResponse({
    description: "Column filter values retrieved successfully",
    status: HttpStatus.OK,
    type: MaterialFilterResponseDto,
  })
  @ApiResponse({
    description: "Invalid column or parameters",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async getMaterialFilters(
    @Query() filterRequest: MaterialFilterRequestDto,
  ): Promise<MaterialFilterResponseDto> {
    return await this.materialService.getColumnFilterValues(filterRequest);
  }

  @Post("page")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Search materials with comprehensive filtering including family-specific criteria and include family-specific data (polymer, filler, elastomer, additive properties). Only materials from POLYMERS, FILLERS, ELASTOMERS, and ADDITIVES families are included, except FEEDSTOCK_RECYCLING_MEMBERS who can only access RECYCLE_POLYMERS. Supports advanced filtering with operators (>=, >, <=, <, =, between, contains) on family-specific properties.",
    summary: "Search materials with family relations and criteria",
  })
  @ApiResponse({
    description: "Materials with family relations retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedMaterialPageResponseDto,
  })
  public async getMaterialsPage(
    @Body() searchRequest: MaterialSearchPageRequestDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<PaginatedMaterialPageResponseDto> {
    const userRole = request?.user?.role;
    return this.materialService.searchMaterialsWithCriteria({ ...searchRequest, userRole });
  }

  @Post("compare")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Compare materials based on their properties. Materials can be from different families, and properties from all families will be included in the response. Returns dynamic properties based on material families in the comparison (Polymers, Fillers, Elastomers, Additives, Recycle Polymers). Access is role-based: Admin, Data Scientist, and Engineering Manager have full access. Engineers can only compare available materials.",
    summary: "Compare multiple materials",
  })
  @ApiResponse({
    description: "Material comparison completed successfully",
    status: HttpStatus.OK,
    type: MaterialComparisonResponseDto,
  })
  @ApiResponse({
    description:
      "Invalid request - invalid material count (must be 1-20 materials)",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description:
      "Business rule violation - Engineers can only compare available materials",
    status: HttpStatus.UNPROCESSABLE_ENTITY,
  })
  @ApiResponse({
    description: "One or more materials not found",
    status: HttpStatus.NOT_FOUND,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async compareMaterials(
    @Body() request: MaterialComparisonRequestDto,
    @Req() requestObject?: AuthenticatedRequest,
  ): Promise<MaterialComparisonResponseDto> {
    const userRole = requestObject?.user?.role;
    return this.materialComparisonService.compareMaterials(request, userRole);
  }

  @Patch(":id")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description: "Update material properties. Only admins and material managers can update materials.",
    summary: "Update material by ID",
  })
  @ApiParam({
    description: "Material ID",
    example: "material-uuid",
    name: "id",
  })
  @ApiResponse({
    description: "Material updated successfully",
    status: HttpStatus.OK,
    type: MaterialSearchResponseDto,
  })
  @ApiResponse({
    description: "Material not found",
    status: HttpStatus.NOT_FOUND,
  })
  @ApiResponse({
    description: "Invalid input data",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async updateMaterial(
    @Param("id") id: string,
    @Body() updateMaterialDto: UpdateMaterialDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<MaterialSearchResponseDto> {
    const userRole = request?.user?.role;
    return this.materialService.updateMaterial(id, updateMaterialDto, userRole);
  }

  @Put()
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description: "Update material properties. Only admins and material managers can update materials.",
    summary: "Batch update material",
  })
  @ApiBody({
    examples: {
      default: {
        value: [
          {
            family: "ADDITIVES",
            familyData: { anonymizationCode: "ANT_002" },
            id: "f4d16359-abe9-4359-a58f-00723b8aa0ee",
            origin: "Europe",
            reference: "Rigonax 069",
            status: "AVAILABLE",
            supplier: "IG 8-01",
            supplierBatchNumber: "BFC 3380",
            type: "Antioxidant",
          },
          {
            family: "POLYMERS",
            familyData: { mfiAv: 27.8 },
            id: "ee219e74-c644-4933-98b1-827760e0bfbf",
            origin: "Asia",
            reference: "PP-H 1200",
            status: "AVAILABLE",
            supplier: "IG 8-01",
            supplierBatchNumber: "BFC 3380",
            type: "Virgin PP Homopolymer",
          },
        ],
      },
    },
    type: [BatchUpdateMaterialRequestDto],
  })
  @ApiResponse({
    description: "Material updated successfully",
    status: HttpStatus.OK,
    type: [BatchUpdateMaterialResponseDto],
  })
  @ApiResponse({
    description: "Material not found",
    status: HttpStatus.NOT_FOUND,
  })
  @ApiResponse({
    description: "Invalid input data",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async batchUpdateMaterial(
    @Body() batchUpdateMaterialRequestDto: BatchUpdateMaterialRequestDto[],
    @Req() request?: AuthenticatedRequest,
  ): Promise<BatchUpdateMaterialResponseDto[]> {
    const userRole = request?.user?.role;
    return this.materialService.batchUpdateMaterial(batchUpdateMaterialRequestDto, userRole);
  }

  @Post("export")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.FEEDSTOCK_RECYCLING_MEMBERS,
    UserRole.MATERIAL_MANAGER,
  )
  @ApiOperation({
    description:
      "Export materials with selected fields as an .xlsx file. Request DTO is the same as /materials/page, but with an additional selectedFields array. selectedFields combines MaterialSearchPageRequestDto and family property definitions.",
    summary: "Export materials as .xlsx with dynamic selected fields",
  })
  @ApiResponse({
    description: "Exported materials as .xlsx file",
    schema: { format: "binary", type: "string" },
    status: HttpStatus.OK,
  })
  public async exportMaterials(
    @Body() materialExportRequestDto: MaterialExportRequestDto,
    @Res() response: Response,
  ): Promise<void> {
    const buffer = await this.materialExportService.exportMaterials(materialExportRequestDto);

    const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
    const family = materialExportRequestDto.family ?? "all";
    const filename = `materials-${family}-${timestamp}.xlsx`;

    response.header("Content-Disposition", `attachment; filename="${filename}"`);
    response.header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.header("Content-Length", buffer.byteLength.toString());
    response.send(buffer);
  }
}
