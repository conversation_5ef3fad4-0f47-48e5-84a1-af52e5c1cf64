import { BadRequestException, Injectable } from "@nestjs/common";
import * as exceljs from "exceljs";
import { MaterialExportRequestDto } from "./dto/material-export-request.dto";
import { MaterialService } from "./material.service";
import { MaterialRepository } from "./repositories/material.repository";
import { ColumnDefinition, getColumnDefinitions } from "./utils/export-columns";
import { MaterialWithFamilyData } from "./index";
import { MaterialFamily } from "@/generated/prisma";

@Injectable()
export class MaterialExportService {
  private readonly cellBorder = {
    bottom: { style: "thin" as const },
    left: { style: "thin" as const },
    right: { style: "thin" as const },
    top: { style: "thin" as const },
  };

  private readonly yellowFill = {
    fgColor: { argb: "FFFFFF00" },
    pattern: "solid" as const,
    type: "pattern" as const,
  };

  public constructor(
    private readonly materialRepository: MaterialRepository,
    private readonly materialService: MaterialService,
  ) {}

  public async exportMaterials(materialExportRequestDto: MaterialExportRequestDto): Promise<exceljs.Buffer> {
    const { selectedFields = [], familyCriteria, statistics, statisticFields } = materialExportRequestDto;

    const family = materialExportRequestDto.family;
    const materials = await this.fetchMaterials(materialExportRequestDto, familyCriteria);

    const fieldsToExport = selectedFields.length > 0
      ? selectedFields
      : this.getAllAvailableFields(materials.data);

    const hasStatistics = Boolean(statistics && statisticFields);
    const workbook = this.createWorkbook(fieldsToExport, family, hasStatistics);
    this.populateMaterialsData(workbook, materials, fieldsToExport, family, hasStatistics);

    if (hasStatistics && statisticFields) {
      this.addStatisticRows(workbook, materials, fieldsToExport, statisticFields, family);
    }

    return workbook.xlsx.writeBuffer();
  }

  private async fetchMaterials(requestDto: MaterialExportRequestDto, familyCriteria: MaterialExportRequestDto["familyCriteria"]) {
    return await this.materialRepository.findWithFamilyFieldFilters({
      ...requestDto,
      familyFilters: familyCriteria,
    });
  }

  private createWorkbook(selectedFields: string[], family: MaterialFamily, hasStatistics: boolean): exceljs.Workbook {
    const workbook = new exceljs.Workbook();
    const worksheet = workbook.addWorksheet(`Materials ${family}`);
    const allColumnDefinitions: ColumnDefinition[] = getColumnDefinitions(family);

    const fieldColumns = selectedFields.map(field => ({
      header: this.getLabel(allColumnDefinitions, field),
      key: field,
      width: 15,
    }));

    const columnsConfig = hasStatistics
      ? [{ header: "", key: "empty", width: 25 }, ...fieldColumns]
      : fieldColumns;

    worksheet.columns = columnsConfig;

    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell, colNumber) => {
      if (colNumber > 1) {
        cell.border = this.cellBorder;
      }
    });

    const defaultRow: Record<string, string> = {};
    if (hasStatistics) {
      defaultRow.empty = "";
    }
    for (const field of selectedFields) {
      defaultRow[field] = "by default";
    }
    const defaultRowAdded = worksheet.addRow(defaultRow);
    defaultRowAdded.eachCell((cell, colNumber) => {
      if (colNumber > 1) {
        cell.fill = this.yellowFill;
        cell.border = this.cellBorder;
      }
    });

    const typeRow: Record<string, string> = {};
    if (hasStatistics) {
      typeRow.empty = "";
    }
    for (const field of selectedFields) {
      typeRow[field] = this.getFieldType(allColumnDefinitions, field);
    }
    const typeRowAdded = worksheet.addRow(typeRow);
    typeRowAdded.eachCell((cell, colNumber) => {
      if (colNumber > 1) {
        cell.border = this.cellBorder;
      }
    });

    return workbook;
  }

  private populateMaterialsData(
    workbook: exceljs.Workbook,
    materials: { data: MaterialWithFamilyData[] },
    selectedFields: string[],
    family: MaterialFamily,
    hasStatistics: boolean,
  ): void {
    const worksheet = workbook.getWorksheet(`Materials ${family}`);

    if (!worksheet) {
      throw new BadRequestException("Worksheet not found");
    }

    for (const material of materials.data) {
      const formattedMaterial = this.formatMaterialForExport(material);
      const row = this.createRowData(formattedMaterial, selectedFields, hasStatistics);
      const addedRow = worksheet.addRow(row);
      addedRow.eachCell((cell, colNumber) => {
        if (colNumber > 1) {
          cell.border = this.cellBorder;
        }
      });
    }
  }

  private formatMaterialForExport(material: MaterialWithFamilyData): Record<string, unknown> {
    const formattedMaterial = this.materialService.mapToMaterialPageResponse(material);
    return {
      ...formattedMaterial,
      ...formattedMaterial.familyData,
    };
  }

  private createRowData(formattedMaterial: Record<string, unknown>, selectedFields: string[], hasStatistics: boolean): Record<string, unknown> {
    const row: Record<string, unknown> = {};

    if (hasStatistics) {
      row.empty = "";
    }

    for (const field of selectedFields) {
      row[field] = formattedMaterial[field] ?? "";
    }

    return row;
  }

  private getAllAvailableFields(materials: MaterialWithFamilyData[]): string[] {
    if (materials.length === 0) {
      return [];
    }

    const firstMaterial = materials[0];
    if (!firstMaterial.family) {
      return [];
    }

    const columnDefinitions = getColumnDefinitions(firstMaterial.family);
    return columnDefinitions.map(col => col.name);
  }

  private getFieldType(allColumnDefinitions: ColumnDefinition[], field: string): string {
    const columnDefinition = allColumnDefinitions.find(property => property.name === field);
    if (columnDefinition) return columnDefinition.type;
    return "Text";
  }

  private getLabel(allColumnDefinitions: ColumnDefinition[], field: string): string {
    const columnDefinition = allColumnDefinitions.find(property => property.name === field);
    if (columnDefinition) return columnDefinition.label;
    return "";
  }

  private addStatisticRows(
    workbook: exceljs.Workbook,
    materials: { data: MaterialWithFamilyData[] },
    selectedFields: string[],
    statisticFields: NonNullable<MaterialExportRequestDto["statisticFields"]>,
    family: MaterialFamily,
  ): void {
    const worksheet = workbook.getWorksheet(`Materials ${family}`);
    if (!worksheet) {
      throw new BadRequestException("Worksheet not found");
    }

    worksheet.addRow({ empty: "" });

    const formattedMaterials = materials.data.map(material => this.formatMaterialForExport(material));

    const statisticOperations: { fields: string[], name: string, operation: string }[] = [];

    if (statisticFields.average && statisticFields.average.length > 0) {
      statisticOperations.push({ fields: statisticFields.average, name: "Average", operation: "average" });
    }

    if (statisticFields.standardDeviation && statisticFields.standardDeviation.length > 0) {
      statisticOperations.push(
        { fields: statisticFields.standardDeviation, name: "Standard Deviation", operation: "standardDeviation" },
      );
    }

    if (statisticFields.coefficientOfVariation && statisticFields.coefficientOfVariation.length > 0) {
      statisticOperations.push(
        { fields: statisticFields.coefficientOfVariation, name: "Coefficient of variation (%)", operation: "coefficientOfVariation" },
      );
    }

    if (statisticFields.max && statisticFields.max.length > 0) {
      statisticOperations.push({ fields: statisticFields.max, name: "Max", operation: "max" });
    }

    if (statisticFields.min && statisticFields.min.length > 0) {
      statisticOperations.push({ fields: statisticFields.min, name: "Min", operation: "min" });
    }

    if (statisticFields.median && statisticFields.median.length > 0) {
      statisticOperations.push({ fields: statisticFields.median, name: "Median", operation: "median" });
    }

    for (const stat of statisticOperations) {
      const statisticRow: Record<string, string | number> = {};

      statisticRow.empty = stat.name;

      for (const field of selectedFields) {
        if (stat.fields.includes(field)) {
          const values = this.extractNumericValues(formattedMaterials, field);
          statisticRow[field] = values.length > 0 ? this.performStatisticOperation(values, stat.operation) : "";
        }
        else {
          statisticRow[field] = "";
        }
      }

      const addedRow = worksheet.addRow(statisticRow);
      addedRow.eachCell((cell, colNumber) => {
        cell.border = this.cellBorder;
        if (colNumber === 1) {
          cell.fill = this.yellowFill;
        }
        else {
          cell.font = { bold: true };
        }
      });
    }
  }

  private extractNumericValues(materials: Record<string, unknown>[], field: string): number[] {
    return materials
      .map(material => material[field])
      .filter((value): value is number => typeof value === "number" && !Number.isNaN(value));
  }

  private performStatisticOperation(values: number[], operation: string): number {
    switch (operation) {
      case "average": {
        return this.calculateAverage(values);
      }
      case "standardDeviation": {
        return this.calculateStandardDeviation(values);
      }
      case "coefficientOfVariation": {
        return this.calculateCoefficientOfVariation(values);
      }
      case "max": {
        return this.calculateMax(values);
      }
      case "min": {
        return this.calculateMin(values);
      }
      case "median": {
        return this.calculateMedian(values);
      }
      default: {
        return 0;
      }
    }
  }

  private calculateAverage(values: number[]): number {
    return Math.round((values.reduce((sum, value) => sum + value, 0) / values.length) * 1000) / 1000;
  }

  private calculateStandardDeviation(values: number[]): number {
    const avg = this.calculateAverage(values);
    const squaredDiffs = values.map(value => Math.pow(value - avg, 2));
    const variance = squaredDiffs.reduce((sum, value) => sum + value, 0) / values.length;
    return Math.round(Math.sqrt(variance) * 1000) / 1000;
  }

  private calculateCoefficientOfVariation(values: number[]): number {
    const avg = this.calculateAverage(values);
    const standardDeviation = this.calculateStandardDeviation(values);
    return avg === 0 ? 0 : Math.round((standardDeviation / avg) * 100 * 1000) / 1000;
  }

  private calculateMax(values: number[]): number {
    return Math.max(...values);
  }

  private calculateMin(values: number[]): number {
    return Math.min(...values);
  }

  private calculateMedian(values: number[]): number {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }
}
