import { Module } from "@nestjs/common";
import { DepartmentService } from "../department/department.service";
import { DepartmentRepository } from "../department/repositories/department.repository";
import { LocationService } from "../location/location.service";
import { LocationRepository } from "../location/repositories/location.repository";
import { PrismaService } from "../prisma.service";
import { RoleRepository } from "../role/repositories/role.repository";
import { RoleService } from "../role/role.service";
import { UserRepository } from "../user/repositories/user.repository";
import { UserService } from "../user/user.service";
import { MaterialComparisonService } from "./material-comparison.service";
import { MaterialExportService } from "./material-export.service";
import { MaterialController } from "./material.controller";
import { MaterialService } from "./material.service";
import { MaterialComparisonRepository } from "./repositories/material-comparison.repository";
import { MaterialFilterRepository } from "./repositories/material-filter.repository";
import { MaterialRepository } from "./repositories/material.repository";
import { PropertyMetadataRepository } from "./repositories/property-metadata.repository";
import { PropertyMetadataService } from "./services/property-metadata.service";
import { AuthService } from "@/auth/auth.service";
import { SupplierRepository } from "@/supplier/repositories/supplier.repository";
import { SupplierService } from "@/supplier/supplier.service";

@Module({
  controllers: [MaterialController],
  exports: [MaterialRepository, MaterialService, MaterialComparisonService, PropertyMetadataService, MaterialExportService],
  providers: [
    MaterialService,
    MaterialExportService,
    MaterialComparisonService,
    PropertyMetadataService,
    MaterialRepository,
    MaterialComparisonRepository,
    MaterialFilterRepository,
    PropertyMetadataRepository,
    PrismaService,
    AuthService,
    UserService,
    UserRepository,
    LocationService,
    LocationRepository,
    DepartmentService,
    DepartmentRepository,
    RoleService,
    RoleRepository,
    SupplierService,
    SupplierRepository,
  ],
})
export class MaterialModule {}
