export enum MATERIAL_FILTER_COLUMN {
  ORIGIN = "origin",
  STATUS = "status",
  TYPE = "type",
  SUPPLIER = "supplier",
  SUPPLIER_BATCH_NUMBER = "supplierBatchNumber",
  REFERENCE = "reference",
  ADDITIVE_ANONYMIZATION_CODE = "additive.anonymizationCode",
  ELASTOMER_CODEANONYM_ELASTO = "elastomer.codeanonymElasto",
  FILLER_CODEANONYM_FILLER = "filler.codeanonymFiller",
  FILLER_FORM = "filler.form",
  POLYMER_TDS = "polymer.tds",
  POLYMER_COMMENT = "polymer.comment",
  POLYMER_MFI_NORME = "polymer.mfiNorme",
  POLYMER_MFI_TEST_CONDITIONS = "polymer.mfiTestConditions",
  POLYMER_DENSITY_NORME = "polymer.densityNorme",
  POLYMER_TENSILE_MODULUS_NORME = "polymer.tensileModulusNorme",
  POLYMER_TENSILE_MODULUS_COND = "polymer.tensileModulusCond",
  POLYMER_FLEXURAL_MODULUS_NORME = "polymer.flexuralModulusNorme",
  POLYMER_STRESS_BREAK_NORME = "polymer.stressBreakNorme",
  POLYMER_STRAIN_BREAK_NORME = "polymer.strainBreakNorme",
  POLYMER_NOTCHED_IZOD_NORME = "polymer.notchedIzodNorme",
  POLYMER_NOTCHED_IZOD_FAILURE_TYPE = "polymer.notchedIzodFailureType",
  POLYMER_UNNOTCHED_IZOD_NORME = "polymer.unnotchedIzodNorme",
  POLYMER_UNNOTCHED_IZOD_FAILURE_TYPE = "polymer.unnotchedIzodFailureType",
  POLYMER_HDT_B_NORME = "polymer.hdtBNorme",
  RECYCLE_POLYMER_TECHNICAL_PROFILE_AVG_VALUE = "recyclePolymer.technicalProfileAvgValue",
  RECYCLE_POLYMER_COLOR = "recyclePolymer.color",
  RECYCLE_POLYMER_PIR_PCR_ELV = "recyclePolymer.pirPcrElv",
  RECYCLE_POLYMER_WASTE_DETAILS = "recyclePolymer.wasteDetails",
  RECYCLE_POLYMER_MATERIAL_FORM = "recyclePolymer.materialForm",
  RECYCLE_POLYMER_TDS = "recyclePolymer.tds",
  RECYCLE_POLYMER_PRICE_EXW_DATE = "recyclePolymer.priceExwDate",
  RECYCLE_POLYMER_CERTIFICATES_REACH_ROHS = "recyclePolymer.certificatesReachRohs",
  RECYCLE_POLYMER_END_OF_WASTE_STATUS = "recyclePolymer.endOfWasteStatus",
  RECYCLE_POLYMER_COMMENT = "recyclePolymer.comment",
  RECYCLE_POLYMER_FILTRATION_LOCATION = "recyclePolymer.filtrationLocation",
  RECYCLE_POLYMER_SYSTEM_FILTRATION = "recyclePolymer.systemFiltration",
  RECYCLE_POLYMER_FILTRATION_SIZE = "recyclePolymer.filtrationSize",
  RECYCLE_POLYMER_LEVEL_OF_POLLUTION = "recyclePolymer.levelOfPollution",
  RECYCLE_POLYMER_VENTING = "recyclePolymer.venting",
  RECYCLE_POLYMER_VACUUM_PRESSURE = "recyclePolymer.vacuumPressure",
  RECYCLE_POLYMER_SCREW_SPEED = "recyclePolymer.screwSpeed",
  RECYCLE_POLYMER_SCREW_PROFILE = "recyclePolymer.screwProfile",
  RECYCLE_POLYMER_MFI_NORME = "recyclePolymer.mfiNorme",
  RECYCLE_POLYMER_MFI_TEST_CONDITIONS = "recyclePolymer.mfiTestConditions",
  RECYCLE_POLYMER_ASH_CONTENT_NORME = "recyclePolymer.ashContentNorme",
  RECYCLE_POLYMER_DENSITY_NORME = "recyclePolymer.densityNorme",
  RECYCLE_POLYMER_TENSILE_MODULUS_NORME = "recyclePolymer.tensileModulusNorme",
  RECYCLE_POLYMER_TENSILE_MODULUS_CONDITIONS = "recyclePolymer.tensileModulusConditions",
  RECYCLE_POLYMER_FLEXURAL_MODULUS_NORME = "recyclePolymer.flexuralModulusNorme",
  RECYCLE_POLYMER_STRESS_BREAK_NORME = "recyclePolymer.stressBreakNorme",
  RECYCLE_POLYMER_STRAIN_BREAK_NORME = "recyclePolymer.strainBreakNorme",
  RECYCLE_POLYMER_NOTCHED_IZOD_NORME = "recyclePolymer.notchedIzodNorme",
  RECYCLE_POLYMER_NOTCHED_IZOD_FAILURE_TYPE = "recyclePolymer.notchedIzodFailureType",
  RECYCLE_POLYMER_UNNOTCHED_IZOD_NORME = "recyclePolymer.unnotchedIzodNorme",
  RECYCLE_POLYMER_UNNOTCHED_IZOD_FAILURE_TYPE = "recyclePolymer.unnotchedIzodFailureType",
  RECYCLE_POLYMER_HDT_B_NORME = "recyclePolymer.hdtBNorme",
  RECYCLE_POLYMER_ODOR_NORME = "recyclePolymer.odorNorme",
  RECYCLE_POLYMER_ODOR_NOTE_1 = "recyclePolymer.odorNote1",
  RECYCLE_POLYMER_ODOR_NOTE_2 = "recyclePolymer.odorNote2",
  RECYCLE_POLYMER_ODOR_NOTE_3 = "recyclePolymer.odorNote3",
  RECYCLE_POLYMER_VOC_FOG_NORME = "recyclePolymer.vocFogNorme",
  RECYCLE_POLYMER_CMA_23_NORM = "recyclePolymer.cma23Norm",
  RECYCLE_POLYMER_CMA_23_MEAN_BREAK_TYPE = "recyclePolymer.cma23MeanBreakType",
  RECYCLE_POLYMER_CMA_0_NORM = "recyclePolymer.cma0Norm",
  RECYCLE_POLYMER_CMA_0_MEAN_BREAK_TYPE = "recyclePolymer.cma0MeanBreakType",
  RECYCLE_POLYMER_CMA_10_NORM = "recyclePolymer.cma10Norm",
  RECYCLE_POLYMER_CMA_10_MEAN_BREAK_TYPE = "recyclePolymer.cma10MeanBreakType",
  RECYCLE_POLYMER_CMA_20_NORM = "recyclePolymer.cma20Norm",
  RECYCLE_POLYMER_CMA_20_MEAN_BREAK_TYPE = "recyclePolymer.cma20MeanBreakType",
  RECYCLE_POLYMER_CMA_30_NORM = "recyclePolymer.cma30Norm",
  RECYCLE_POLYMER_CMA_30_MEAN_BREAK_TYPE = "recyclePolymer.cma30MeanBreakType",
}
