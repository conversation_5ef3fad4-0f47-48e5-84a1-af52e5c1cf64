import { Injectable } from "@nestjs/common";
import { MATERIAL_FILTER_COLUMN } from "../enum/material-filter-column.enum";
import { MaterialStatus, Prisma } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

type ModelName = "additive" | "elastomer" | "filler" | "material" | "polymer" | "recyclePolymer";

interface PrismaModel {
  findMany(arguments_?: {
    where?: Record<string, unknown>
    select?: Record<string, boolean>
    distinct?: string[]
    orderBy?: Record<string, string>
    skip?: number
    take?: number
  }): Promise<Record<string, unknown>[]>
}

export interface PaginatedFilterResult {
  data: string[]
  total: number
  page: number
  limit: number
}

@Injectable()
export class MaterialFilterRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findDistinctColumnValues(
    column: MATERIAL_FILTER_COLUMN,
    searchQuery?: string,
    page = 1,
    limit = 10,
  ): Promise<PaginatedFilterResult> {
    const skip = (page - 1) * limit;
    const paginationParameters = { limit, page, skip };

    if (column === MATERIAL_FILTER_COLUMN.SUPPLIER) {
      return this.handleSupplier(searchQuery, paginationParameters);
    }
    return this.handleMaterialWithFamily(column, searchQuery, paginationParameters);
  }

  private async handleSupplier(
    searchQuery?: string,
    pagination?: { limit: number, page: number, skip: number },
  ): Promise<PaginatedFilterResult> {
    const { limit = 10, page = 1, skip = 0 } = pagination ?? {};

    const distinctSupplierIds = await this.prisma.material.findMany({
      distinct: ["supplierId"],
      select: { supplierId: true },
      where: { supplierId: { not: undefined } },
    });

    const supplierIds = distinctSupplierIds.map(m => m.supplierId).filter(Boolean);
    const whereCondition = {
      id: { in: supplierIds },
      ...(searchQuery ? { name: { contains: searchQuery, mode: "insensitive" as const } } : {}),
    };

    const [suppliers, total] = await Promise.all([
      this.prisma.supplier.findMany({
        orderBy: { name: "asc" },
        select: { name: true },
        skip,
        take: limit,
        where: whereCondition,
      }),
      this.prisma.supplier.count({ where: whereCondition }),
    ]);

    return {
      data: suppliers.map(s => s.name),
      limit,
      page,
      total,
    };
  }

  private async handleMaterialWithFamily(
    column: string,
    searchQuery?: string,
    pagination?: { limit: number, page: number, skip: number },
  ): Promise<PaginatedFilterResult> {
    const { limit = 10, page = 1 } = pagination ?? {};

    const [data, total] = await this.execute(column, searchQuery, pagination);

    if (!Array.isArray(data) || data.length === 0 || total === 0) {
      return this.createEmptyResult();
    }

    const fieldName = column.includes(".") ? column.split(".")[1] : column;
    return this.formatResult(data, total as number, fieldName, limit, page);
  }

  private buildWhereCondition(fieldName: string, searchQuery?: string): Prisma.MaterialWhereInput {
    const condition: Prisma.MaterialWhereInput = {};

    if (searchQuery) {
      if (fieldName === "status") {
        const validStatuses = Object.values(MaterialStatus);
        const isValidStatus = validStatuses.includes(searchQuery as MaterialStatus);
        if (isValidStatus) {
          condition[fieldName] = {
            equals: searchQuery as MaterialStatus,
          };
        }
      }
      else {
        condition[fieldName] = {
          contains: searchQuery,
          mode: "insensitive" as const,
        };
      }
    }
    else {
      condition[fieldName] = { not: undefined };
    }

    return condition;
  }

  private execute(
    column: string,
    searchQuery?: string,
    pagination?: { limit: number, page: number, skip: number },
  ) {
    const { limit = 10, skip = 0 } = pagination ?? {};

    const { fieldName, model } = this.parseColumnToModelAndField(column);

    if (!this.isValidField(model, fieldName)) {
      return Promise.resolve([[], 0]);
    }

    const whereCondition = this.buildWhereCondition(fieldName, searchQuery);
    return this.executeDistinctQueries(model, fieldName, whereCondition, skip, limit);
  }

  private parseColumnToModelAndField(column: string): { fieldName: string, model: ModelName } {
    const modelPrefixes: ModelName[] = ["additive", "elastomer", "filler", "polymer", "recyclePolymer"];

    for (const model of modelPrefixes) {
      if (column.startsWith(`${model}.`)) {
        return {
          fieldName: column.split(".")[1],
          model,
        };
      }
    }

    return {
      fieldName: column,
      model: "material",
    };
  }

  private getValidFieldsForModel(model: ModelName): string[] {
    const allColumns = Object.values(MATERIAL_FILTER_COLUMN);

    if (model === "material") {
      return allColumns
        .filter(column => !column.includes("."))
        .filter(column => column !== MATERIAL_FILTER_COLUMN.SUPPLIER);
    }

    return allColumns
      .filter(column => column.startsWith(`${model}.`))
      .map(column => column.split(".")[1]);
  }

  private isValidField(model: ModelName, fieldName: string): boolean {
    const validFields = this.getValidFieldsForModel(model);
    return validFields.includes(fieldName);
  }

  private executeDistinctQueries(
    model: ModelName,
    fieldName: string,
    whereCondition: Prisma.MaterialWhereInput,
    skip: number,
    limit: number,
  ) {
    const modelType = this.prisma[model] as PrismaModel;

    return Promise.all([
      modelType.findMany({
        distinct: [fieldName],
        orderBy: { [fieldName]: "asc" },
        select: { [fieldName]: true },
        skip,
        take: limit,
        where: whereCondition,
      }),
      modelType.findMany({
        distinct: [fieldName],
        select: { [fieldName]: true },
        where: whereCondition,
      }).then(data => data.length),
    ]);
  }

  private formatResult(data: unknown[], total: number, fieldName: string, limit: number, page: number): PaginatedFilterResult {
    return {
      data: data.map((item) => {
        const value = (item as Record<string, unknown>)[fieldName];
        return typeof value === "string" ? value : undefined;
      }).filter((value): value is string => value !== undefined && value !== ""),
      limit,
      page,
      total,
    };
  }

  private createEmptyResult(): PaginatedFilterResult {
    return { data: [], limit: 0, page: 0, total: 0 };
  }
}
