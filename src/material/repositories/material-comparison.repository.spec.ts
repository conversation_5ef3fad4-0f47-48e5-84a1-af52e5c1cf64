import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { MaterialComparisonRepository } from "./material-comparison.repository";
import { PropertyMetadataRepository } from "./property-metadata.repository";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

interface MockPrismaService {
  material: {
    findMany: jest.Mock
  }
}

interface MockPropertyMetadataRepository {
  findByFamily: jest.Mock
}

describe("MaterialComparisonRepository", () => {
  let repo: MaterialComparisonRepository;
  let prisma: MockPrismaService;
  let propertyMetadataRepo: MockPropertyMetadataRepository;

  beforeEach(async () => {
    prisma = {
      material: {
        findMany: jest.fn(),
      },
    };
    propertyMetadataRepo = {
      findByFamily: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialComparisonRepository,
        { provide: PrismaService, useValue: prisma },
        { provide: PropertyMetadataRepository, useValue: propertyMetadataRepo },
      ],
    }).compile();

    repo = module.get(MaterialComparisonRepository);
  });

  describe("getMaterialsForComparison", () => {
    it("should fetch materials and map to comparison DTOs", async () => {
      const materials = [
        {
          additive: undefined,
          elastomer: undefined,
          family: MaterialFamily.POLYMERS,
          filler: undefined,
          id: "1",
          origin: "Asia",
          polymer: { density: 1.2 },
          recyclePolymer: undefined,
          reference: "REF1",
          status: MaterialStatus.AVAILABLE,
          supplier: { id: "S1", name: "Supplier1" },
          supplierBatchNumber: "B1",
          type: "TYPE1",
        },
      ];
      prisma.material.findMany.mockResolvedValue(materials);
      propertyMetadataRepo.findByFamily.mockResolvedValue([{ propertyKey: "density" }]);

      const result = await repo.getMaterialsForComparison(["1"]);

      expect(prisma.material.findMany).toHaveBeenCalledWith({
        include: {
          additive: true,
          elastomer: true,
          filler: true,
          polymer: true,
          recyclePolymer: true,
          supplier: true,
        },
        where: { id: { in: ["1"] } },
      });
      expect(result[0]).toMatchObject({
        id: "1",
        properties: { density: 1.2 },
        supplierName: "Supplier1",
      });
    });

    it("should apply statusFilter if provided", async () => {
      prisma.material.findMany.mockResolvedValue([]);
      await repo.getMaterialsForComparison(["1"], MaterialStatus.AVAILABLE);
      expect(prisma.material.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: { in: ["1"] }, status: MaterialStatus.AVAILABLE },
        }),
      );
    });
  });

  describe("validateMaterialFamily", () => {
    it("should return the family if all materials have the same family", async () => {
      prisma.material.findMany.mockResolvedValue([
        { family: MaterialFamily.POLYMERS },
        { family: MaterialFamily.POLYMERS },
      ]);
      const result = await repo.validateMaterialFamily(["1", "2"]);
      expect(result).toBe(MaterialFamily.POLYMERS);
    });

    it("should return null if materials have different families", async () => {
      prisma.material.findMany.mockResolvedValue([
        { family: MaterialFamily.POLYMERS },
        { family: MaterialFamily.FILLERS },
      ]);
      const result = await repo.validateMaterialFamily(["1", "2"]);
      expect(result).toBeUndefined();
    });

    it("should return null if no materials found", async () => {
      prisma.material.findMany.mockResolvedValue([]);
      const result = await repo.validateMaterialFamily(["1", "2"]);
      expect(result).toBeUndefined();
    });
  });
});
