import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { MaterialComparisonDto } from "../dto";
import { PropertyMetadataRepository } from "./property-metadata.repository";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

@Injectable()
export class MaterialComparisonRepository {
  public constructor(
    private readonly prisma: PrismaService,
    private readonly propertyMetadataRepository: PropertyMetadataRepository,
  ) {}

  public async getMaterialsForComparison(materialIds: string[], statusFilter?: MaterialStatus): Promise<MaterialComparisonDto[]> {
    const whereClause: Record<string, unknown> = {
      id: {
        in: materialIds,
      },
    };

    if (statusFilter) {
      whereClause.status = statusFilter;
    }

    const materials = await this.prisma.material.findMany({
      include: {
        additive: true,
        elastomer: true,
        filler: true,
        polymer: true,
        recyclePolymer: true,
        supplier: true,
      },
      where: whereClause,
    });

    return Promise.all(materials.map(material => this.mapMaterialToComparisonDto(material)));
  }

  public async validateMaterialFamily(materialIds: string[]): Promise<MaterialFamily | undefined> {
    const materials = await this.prisma.material.findMany({
      select: {
        family: true,
      },
      where: {
        id: {
          in: materialIds,
        },
      },
    });

    if (materials.length === 0) {
      return undefined;
    }

    const firstFamily = materials[0].family;
    const allSameFamily = materials.every(material => material.family === firstFamily);

    return allSameFamily ? firstFamily : undefined;
  }

  private async mapMaterialToComparisonDto(material: Record<string, unknown>): Promise<MaterialComparisonDto> {
    const baseDto: MaterialComparisonDto = {
      family: material.family as MaterialFamily,
      id: material.id as string,
      origin: material.origin as string,
      properties: {},
      reference: material.reference as string,
      status: material.status as MaterialStatus,
      supplierBatchNumber: material.supplierBatchNumber as string,
      supplierId: (material.supplier as Record<string, unknown>).id as string,
      supplierName: (material.supplier as Record<string, unknown>).name as string,
      type: material.type as string,
    };

    switch (material.family) {
      case MaterialFamily.POLYMERS: {
        if (material.polymer) {
          baseDto.properties = await this.extractPropertiesFromObject(material.polymer as Record<string, unknown>, material.family);
        }
        break;
      }
      case MaterialFamily.FILLERS: {
        if (material.filler) {
          baseDto.properties = await this.extractPropertiesFromObject(material.filler as Record<string, unknown>, material.family);
        }
        break;
      }
      case MaterialFamily.ELASTOMERS: {
        if (material.elastomer) {
          baseDto.properties = await this.extractPropertiesFromObject(material.elastomer as Record<string, unknown>, material.family);
        }
        break;
      }
      case MaterialFamily.ADDITIVES: {
        if (material.additive) {
          baseDto.properties = await this.extractPropertiesFromObject(material.additive as Record<string, unknown>, material.family);
        }
        break;
      }
      case MaterialFamily.RECYCLE_POLYMERS: {
        if (material.recyclePolymer) {
          baseDto.properties = await this.extractPropertiesFromObject(material.recyclePolymer as Record<string, unknown>, material.family);
        }
        break;
      }
      default: {
        break;
      }
    }

    return baseDto;
  }

  private async extractPropertiesFromObject(
    sourceObject: Record<string, unknown>,
    family: MaterialFamily,
  ): Promise<Record<string, unknown>> {
    const result: Record<string, unknown> = {};
    const propertyMetadata = await this.propertyMetadataRepository.findByFamily(family);
    const propertiesToExtract = propertyMetadata.map(property => property.propertyKey);

    for (const property of propertiesToExtract) {
      if (sourceObject[property] !== undefined) {
        result[property] = sourceObject[property];
      }
    }

    return result;
  }
}
