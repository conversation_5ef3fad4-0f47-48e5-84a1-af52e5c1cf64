import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";
import { PropertyMetadataDto } from "../dto";
import { MaterialFamily, PropertyType } from "@/generated/prisma";

@Injectable()
export class PropertyMetadataRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findByFamily(family: MaterialFamily): Promise<PropertyMetadataDto[]> {
    return this.prisma.propertyMetadata.findMany({
      orderBy: { propertyKey: "asc" },
      where: { family },
    });
  }

  public async findByFamilies(families: MaterialFamily[]): Promise<PropertyMetadataDto[]> {
    return this.prisma.propertyMetadata.findMany({
      orderBy: [{ family: "asc" }, { propertyKey: "asc" }],
      where: { family: { in: families } },
    });
  }

  public async findAll(): Promise<PropertyMetadataDto[]> {
    return this.prisma.propertyMetadata.findMany({
      orderBy: [{ family: "asc" }, { propertyKey: "asc" }],
    });
  }

  public async findByPropertyKey(propertyKey: string, family?: MaterialFamily): Promise<PropertyMetadataDto | null> {
    const where: Record<string, unknown> = { propertyKey };
    if (family) {
      where.family = family;
    }

    return this.prisma.propertyMetadata.findFirst({ where });
  }

  public async createMany(properties: Omit<PropertyMetadataDto, "id">[]): Promise<number> {
    const result = await this.prisma.propertyMetadata.createMany({
      data: properties,
      skipDuplicates: true,
    });
    return result.count;
  }

  public async upsert(propertyKey: string, family: MaterialFamily, data: Partial<PropertyMetadataDto>): Promise<PropertyMetadataDto> {
    return this.prisma.propertyMetadata.upsert({
      create: {
        description: data.description ?? "",
        family,
        propertyKey,
        type: data.type ?? PropertyType.STRING,
        unit: data.unit,
      },
      update: data,
      where: {
        ["propertyKey_family"]: {
          family,
          propertyKey,
        },
      },
    });
  }

  public async deleteAll(): Promise<number> {
    const result = await this.prisma.propertyMetadata.deleteMany();
    return result.count;
  }
}
