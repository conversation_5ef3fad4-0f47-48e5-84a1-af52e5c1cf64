import { Test, TestingModule } from "@nestjs/testing";
import { MATERIAL_FILTER_COLUMN } from "../dto/material-filter-request.dto";
import { MaterialFilterRepository } from "./material-filter.repository";
import { MaterialStatus } from "@/generated/prisma";
import { PrismaService } from "@/prisma.service";

describe("MaterialFilterRepository", () => {
  let repository: MaterialFilterRepository;

  const mockPrismaService = {
    additive: {
      findMany: jest.fn(),
    },
    elastomer: {
      findMany: jest.fn(),
    },
    filler: {
      findMany: jest.fn(),
    },
    material: {
      findMany: jest.fn(),
    },
    polymer: {
      findMany: jest.fn(),
    },
    recyclePolymer: {
      findMany: jest.fn(),
    },
    supplier: {
      count: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialFilterRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<MaterialFilterRepository>(MaterialFilterRepository);
  });

  describe("findDistinctColumnValues", () => {
    describe("supplier filtering", () => {
      it("should return distinct supplier names with pagination", async () => {
        const mockSupplierIds = [{ supplierId: "supplier-1" }, { supplierId: "supplier-2" }];
        const mockSuppliers = [{ name: "Supplier A" }, { name: "Supplier B" }];

        mockPrismaService.material.findMany.mockResolvedValue(mockSupplierIds);
        mockPrismaService.supplier.findMany.mockResolvedValue(mockSuppliers);
        mockPrismaService.supplier.count.mockResolvedValue(2);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.SUPPLIER,
          "Supplier",
          1,
          10
        );

        expect(result).toEqual({
          data: ["Supplier A", "Supplier B"],
          limit: 10,
          page: 1,
          total: 2,
        });

        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["supplierId"],
          select: { supplierId: true },
          where: { supplierId: { not: undefined } },
        });

        expect(mockPrismaService.supplier.findMany).toHaveBeenCalledWith({
          orderBy: { name: "asc" },
          select: { name: true },
          skip: 0,
          take: 10,
          where: {
            id: { in: ["supplier-1", "supplier-2"] },
            name: { contains: "Supplier", mode: "insensitive" },
          },
        });
      });

      it("should handle suppliers without search query", async () => {
        const mockSupplierIds = [{ supplierId: "supplier-1" }];
        const mockSuppliers = [{ name: "Test Supplier" }];

        mockPrismaService.material.findMany.mockResolvedValue(mockSupplierIds);
        mockPrismaService.supplier.findMany.mockResolvedValue(mockSuppliers);
        mockPrismaService.supplier.count.mockResolvedValue(1);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.SUPPLIER,
          undefined,
          1,
          10
        );

        expect(result.data).toEqual(["Test Supplier"]);
        expect(mockPrismaService.supplier.findMany).toHaveBeenCalledWith({
          orderBy: { name: "asc" },
          select: { name: true },
          skip: 0,
          take: 10,
          where: {
            id: { in: ["supplier-1"] },
          },
        });
      });
    });

    describe("material columns", () => {
      it("should return distinct material origin values", async () => {
        const mockMaterials = [{ origin: "Europe" }, { origin: "Asia" }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce([{ origin: "Europe" }, { origin: "Asia" }, { origin: "America" }]);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["Europe", "Asia"]);
        expect(result.total).toBe(3);
        expect(result.page).toBe(1);
        expect(result.limit).toBe(10);
      });

      it("should handle material status with exact match search", async () => {
        const mockMaterials = [{ status: MaterialStatus.AVAILABLE }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.STATUS,
          MaterialStatus.AVAILABLE,
          1,
          10
        );

        expect(result.data).toEqual([MaterialStatus.AVAILABLE]);
        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["status"],
          orderBy: { status: "asc" },
          select: { status: true },
          skip: 0,
          take: 10,
          where: {
            status: { equals: MaterialStatus.AVAILABLE },
          },
        });
      });

      it("should handle case-insensitive search for string fields", async () => {
        const mockMaterials = [{ origin: "Europe" }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "europe",
          1,
          10
        );

        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["origin"],
          orderBy: { origin: "asc" },
          select: { origin: true },
          skip: 0,
          take: 10,
          where: {
            origin: { contains: "europe", mode: "insensitive" },
          },
        });
      });

      it("should handle fields without search query", async () => {
        const mockMaterials = [{ origin: "Europe" }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          undefined,
          1,
          10
        );

        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["origin"],
          orderBy: { origin: "asc" },
          select: { origin: true },
          skip: 0,
          take: 10,
          where: {
            origin: { not: undefined },
          },
        });
      });
    });

    describe("family model columns", () => {
      it("should handle polymer columns", async () => {
        const mockPolymerData = [{ comment: "Good quality" }, { comment: "Excellent" }];

        mockPrismaService.polymer.findMany
          .mockResolvedValueOnce(mockPolymerData)
          .mockResolvedValueOnce([{ comment: "Good quality" }, { comment: "Excellent" }, { comment: "Fair" }]);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.POLYMER_COMMENT,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["Good quality", "Excellent"]);
        expect(result.total).toBe(3);
        expect(mockPrismaService.polymer.findMany).toHaveBeenCalledWith({
          distinct: ["comment"],
          orderBy: { comment: "asc" },
          select: { comment: true },
          skip: 0,
          take: 10,
          where: {
            comment: { not: undefined },
          },
        });
      });

      it("should handle additive columns", async () => {
        const mockAdditiveData = [{ anonymizationCode: "ADD001" }];

        mockPrismaService.additive.findMany
          .mockResolvedValueOnce(mockAdditiveData)
          .mockResolvedValueOnce(mockAdditiveData);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ADDITIVE_ANONYMIZATION_CODE,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["ADD001"]);
        expect(mockPrismaService.additive.findMany).toHaveBeenCalled();
      });

      it("should handle elastomer columns", async () => {
        const mockElastomerData = [{ codeanonymElasto: "ELAST001" }];

        mockPrismaService.elastomer.findMany
          .mockResolvedValueOnce(mockElastomerData)
          .mockResolvedValueOnce(mockElastomerData);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ELASTOMER_CODEANONYM_ELASTO,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["ELAST001"]);
        expect(mockPrismaService.elastomer.findMany).toHaveBeenCalled();
      });

      it("should handle filler columns", async () => {
        const mockFillerData = [{ form: "Powder" }];

        mockPrismaService.filler.findMany
          .mockResolvedValueOnce(mockFillerData)
          .mockResolvedValueOnce(mockFillerData);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.FILLER_FORM,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["Powder"]);
        expect(mockPrismaService.filler.findMany).toHaveBeenCalled();
      });

      it("should handle recycle polymer columns", async () => {
        const mockRecyclePolymerData = [{ comment: "Recycled material" }];

        mockPrismaService.recyclePolymer.findMany
          .mockResolvedValueOnce(mockRecyclePolymerData)
          .mockResolvedValueOnce(mockRecyclePolymerData);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.RECYCLE_POLYMER_COMMENT,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["Recycled material"]);
        expect(mockPrismaService.recyclePolymer.findMany).toHaveBeenCalled();
      });
    });

    describe("pagination", () => {
      it("should handle pagination correctly", async () => {
        const mockMaterials = [{ origin: "Europe" }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce([{ origin: "Europe" }, { origin: "Asia" }, { origin: "America" }]);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "",
          2,
          5
        );

        expect(result.page).toBe(2);
        expect(result.limit).toBe(5);
        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["origin"],
          orderBy: { origin: "asc" },
          select: { origin: true },
          skip: 5,
          take: 5,
          where: {
            origin: { not: undefined },
          },
        });
      });

      it("should use default pagination when not provided", async () => {
        const mockMaterials = [{ origin: "Europe" }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN
        );

        expect(result.page).toBe(1);
        expect(result.limit).toBe(10);
        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["origin"],
          orderBy: { origin: "asc" },
          select: { origin: true },
          skip: 0,
          take: 10,
          where: {
            origin: { not: undefined },
          },
        });
      });
    });

    describe("data filtering and formatting", () => {
      it("should return empty result for no data", async () => {
        mockPrismaService.material.findMany.mockResolvedValue([]);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "NonexistentOrigin",
          1,
          10
        );

        expect(result).toEqual({
          data: [],
          limit: 0,
          page: 0,
          total: 0,
        });
      });

      it("should filter out null, undefined and empty string values", async () => {
        const mockMaterials = [
          { origin: "Europe" },
          { origin: undefined },
          { origin: "Asia" },
          { origin: "" },
        ];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["Europe", "Asia"]);
      });

      it("should filter out non-string values", async () => {
        const mockMaterials = [
          { origin: "Europe" },
          { origin: 123 },
          { origin: true },
          { origin: "Asia" },
        ];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        const result = await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.ORIGIN,
          "",
          1,
          10
        );

        expect(result.data).toEqual(["Europe", "Asia"]);
      });
    });

    describe("error handling", () => {
      it("should handle database errors gracefully", async () => {
        mockPrismaService.material.findMany.mockRejectedValue(new Error("Database connection failed"));

        await expect(
          repository.findDistinctColumnValues(MATERIAL_FILTER_COLUMN.ORIGIN, "", 1, 10)
        ).rejects.toThrow("Database connection failed");
      });

      it("should handle invalid column names by returning empty result", async () => {
        mockPrismaService.material.findMany.mockResolvedValue([]);

        const result = await repository.findDistinctColumnValues(
          "invalid.column" as MATERIAL_FILTER_COLUMN,
          "",
          1,
          10
        );

        expect(result).toEqual({
          data: [],
          limit: 0,
          page: 0,
          total: 0,
        });
      });

      it("should handle invalid status search by not filtering", async () => {
        const mockMaterials = [{ status: MaterialStatus.AVAILABLE }];

        mockPrismaService.material.findMany
          .mockResolvedValueOnce(mockMaterials)
          .mockResolvedValueOnce(mockMaterials);

        await repository.findDistinctColumnValues(
          MATERIAL_FILTER_COLUMN.STATUS,
          "INVALID_STATUS",
          1,
          10
        );

        expect(mockPrismaService.material.findMany).toHaveBeenCalledWith({
          distinct: ["status"],
          orderBy: { status: "asc" },
          select: { status: true },
          skip: 0,
          take: 10,
          where: {},
        });
      });
    });
  });
});
