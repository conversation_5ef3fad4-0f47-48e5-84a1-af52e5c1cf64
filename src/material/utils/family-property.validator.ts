import { BadRequestException } from "@nestjs/common";

import { MaterialFamily } from "@/generated/prisma";

interface ValidationCriterion {
  propertyName?: string
  value?: unknown
}

type PropertyType = "string" | "number" | "boolean" | "date";

interface PropertyDefinition {
  name: string
  type: PropertyType
}

/**
 * Get the valid properties with their types for a material family
 * @param family - The material family
 * @returns Array of property definitions with types
 */
function getFamilyPropertyDefinitions(family: MaterialFamily): PropertyDefinition[] {
  const familyMaps: Record<MaterialFamily, PropertyDefinition[]> = {
    [MaterialFamily.ADDITIVES]: [
      { name: "id", type: "string" },
      { name: "materialId", type: "string" },
      { name: "anonymizationCode", type: "string" },
    ],
    [MaterialFamily.ELASTOMERS]: [
      { name: "id", type: "string" },
      { name: "materialId", type: "string" },
      { name: "density", type: "number" },
      { name: "mfi190216", type: "number" },
      { name: "codeanonymElasto", type: "string" },
      { name: "nIzod23", type: "number" },
      { name: "flexModulus", type: "number" },
      { name: "tracModulus100", type: "number" },
      { name: "elongAtBreak", type: "number" },
      { name: "mfi230216", type: "number" },
      { name: "meltingPoint", type: "number" },
      { name: "hdtB", type: "number" },
      { name: "hdtA", type: "number" },
      { name: "shoreA", type: "number" },
      { name: "shoreD", type: "number" },
    ],
    [MaterialFamily.FILLERS]: [
      { name: "id", type: "string" },
      { name: "materialId", type: "string" },
      { name: "codeanonymFiller", type: "string" },
      { name: "bet", type: "number" },
      { name: "d50", type: "number" },
      { name: "d95d05", type: "number" },
      { name: "whiteness", type: "number" },
      { name: "form", type: "string" },
    ],
    [MaterialFamily.POLYMERS]: [
      { name: "id", type: "string" },
      { name: "materialId", type: "string" },
      { name: "resultUpdateDate", type: "date" },
      { name: "validationEngineeringAndFeedstockOrUseInCompounds", type: "date" },
      { name: "tds", type: "string" },
      { name: "priceExw", type: "number" },
      { name: "priceExwDate", type: "string" },
      { name: "comment", type: "string" },
      { name: "mfiNorme", type: "string" },
      { name: "mfiTestConditions", type: "string" },
      { name: "mfiAv", type: "number" },
      { name: "mfiStdDv", type: "number" },
      { name: "densityNorme", type: "string" },
      { name: "densityAv", type: "number" },
      { name: "densityStdDv", type: "number" },
      { name: "tensileModulusNorme", type: "string" },
      { name: "tensileModulusCond", type: "string" },
      { name: "tensileModulusAv", type: "number" },
      { name: "tensileModulusStdDv", type: "number" },
      { name: "flexuralModulusNorme", type: "string" },
      { name: "flexuralModulusAv", type: "number" },
      { name: "flexuralModulusStdDev", type: "number" },
      { name: "flexuralStressFcAv", type: "number" },
      { name: "flexuralStressFcStdDev", type: "number" },
      { name: "stressBreakNorme", type: "string" },
      { name: "stressBreakAv", type: "number" },
      { name: "stressBreakStdDv", type: "number" },
      { name: "stressYieldAv", type: "number" },
      { name: "stressYieldStdDv", type: "number" },
      { name: "yieldStrainAv", type: "number" },
      { name: "yieldStrainStdDv", type: "number" },
      { name: "strainBreakNorme", type: "string" },
      { name: "strainBreakAv", type: "number" },
      { name: "strainBreakStdDv", type: "number" },
      { name: "nominalStrainBreakAv", type: "number" },
      { name: "nominalStrainBreakStdDv", type: "number" },
      { name: "notchedIzodNorme", type: "string" },
      { name: "notchedIzodAv", type: "number" },
      { name: "notchedIzodStdDv", type: "number" },
      { name: "notchedIzodFailureType", type: "string" },
      { name: "unnotchedIzodNorme", type: "string" },
      { name: "unnotchedIzodAv", type: "number" },
      { name: "unnotchedIzodStdDv", type: "number" },
      { name: "unnotchedIzodFailureType", type: "string" },
      { name: "hdtBNorme", type: "string" },
      { name: "hdtBAv", type: "number" },
      { name: "hdtBStdDv", type: "number" },
      { name: "cma23Norm", type: "string" },
      { name: "cma23V", type: "number" },
      { name: "cma23NbSamples", type: "number" },
      { name: "cma23MeanBreakType", type: "string" },
      { name: "cma23EnergyForceMaxAv", type: "number" },
      { name: "cma23EnergyForceMaxStdDev", type: "number" },
      { name: "cma23EnergyPunctureAv", type: "number" },
      { name: "cma23EnergyPunctureStdDev", type: "number" },
      { name: "cma0Norm", type: "string" },
      { name: "cma0V", type: "number" },
      { name: "cma0NbSamples", type: "number" },
      { name: "cma0MeanBreakType", type: "string" },
      { name: "cma0EnergyForceMaxAv", type: "number" },
      { name: "cma0EnergyForceMaxStdDev", type: "number" },
      { name: "cma0EnergyPunctureAv", type: "number" },
      { name: "cma0EnergyPunctureStdDev", type: "number" },
      { name: "cma10Norm", type: "string" },
      { name: "cma10V", type: "number" },
      { name: "cma10NbSamples", type: "number" },
      { name: "cma10MeanBreakType", type: "string" },
      { name: "cma10EnergyForceMaxAv", type: "number" },
      { name: "cma10EnergyForceMaxStdDev", type: "number" },
      { name: "cma10EnergyPunctureAv", type: "number" },
      { name: "cma10EnergyPunctureStdDev", type: "number" },
      { name: "cma20Norm", type: "string" },
      { name: "cma20V", type: "number" },
      { name: "cma20NbSamples", type: "number" },
      { name: "cma20MeanBreakType", type: "string" },
      { name: "cma20EnergyForceMaxAv", type: "number" },
      { name: "cma20EnergyForceMaxStdDev", type: "number" },
      { name: "cma20EnergyPunctureAv", type: "number" },
      { name: "cma20EnergyPunctureStdDev", type: "number" },
      { name: "cma30Norm", type: "string" },
      { name: "cma30V", type: "number" },
      { name: "cma30NbSamples", type: "number" },
      { name: "cma30MeanBreakType", type: "string" },
      { name: "cma30EnergyForceMaxAv", type: "number" },
      { name: "cma30EnergyForceMaxStdDev", type: "number" },
      { name: "cma30EnergyPunctureAv", type: "number" },
      { name: "cma30EnergyPunctureStdDev", type: "number" },
    ],
    [MaterialFamily.RECYCLE_POLYMERS]: [
      { name: "id", type: "string" },
      { name: "materialId", type: "string" },
      { name: "technicalProfileAvgValue", type: "string" },
      { name: "resultUpdateDate", type: "date" },
      { name: "validationEngineeringAndFeedstockOrUseInCompounds", type: "date" },
      { name: "color", type: "string" },
      { name: "pirPcrElv", type: "string" },
      { name: "wasteDetails", type: "string" },
      { name: "materialForm", type: "string" },
      { name: "tds", type: "string" },
      { name: "productVolumesKtY", type: "number" },
      { name: "volumesAvailableForMactKtY", type: "number" },
      { name: "priceExw", type: "number" },
      { name: "priceExwDate", type: "string" },
      { name: "certificatesReachRohs", type: "string" },
      { name: "endOfWasteStatus", type: "string" },
      { name: "comment", type: "string" },
      { name: "filtrationLocation", type: "string" },
      { name: "systemFiltration", type: "string" },
      { name: "filtrationSize", type: "string" },
      { name: "quantityFilteredRemaining", type: "number" },
      { name: "d22NbFiltersUsed", type: "number" },
      { name: "d22NbFilterPerKgFeedstock", type: "number" },
      { name: "d32QuantityScrap", type: "number" },
      { name: "d32NbFilterPerKgFeedstock", type: "number" },
      { name: "levelOfPollution", type: "string" },
      { name: "venting", type: "string" },
      { name: "trialDate", type: "date" },
      { name: "vacuumPressure", type: "string" },
      { name: "screwSpeed", type: "string" },
      { name: "screwProfile", type: "string" },
      { name: "mfiNorme", type: "string" },
      { name: "mfiTestConditions", type: "string" },
      { name: "mfiAv", type: "number" },
      { name: "mfiStdDv", type: "number" },
      { name: "ashContentNorme", type: "string" },
      { name: "ashContentAv", type: "number" },
      { name: "ashContentStdDv", type: "number" },
      { name: "densityNorme", type: "string" },
      { name: "densityAv", type: "number" },
      { name: "densityStdDv", type: "number" },
      { name: "tensileModulusNorme", type: "string" },
      { name: "tensileModulusConditions", type: "string" },
      { name: "tensileModulusAv", type: "number" },
      { name: "tensileModulusStdDv", type: "number" },
      { name: "flexuralModulusNorme", type: "string" },
      { name: "flexuralModulusAv", type: "number" },
      { name: "flexuralModulusStdDev", type: "number" },
      { name: "flexuralStressFcAv", type: "number" },
      { name: "flexuralStressFcStdDev", type: "number" },
      { name: "stressBreakNorme", type: "string" },
      { name: "stressBreakAv", type: "number" },
      { name: "stressBreakStdDv", type: "number" },
      { name: "stressYieldAv", type: "number" },
      { name: "stressYieldStdDv", type: "number" },
      { name: "yieldStrainAv", type: "number" },
      { name: "yieldStrainStdDv", type: "number" },
      { name: "strainBreakNorme", type: "string" },
      { name: "strainBreakAv", type: "number" },
      { name: "strainBreakStdDv", type: "number" },
      { name: "nominalStrainBreakAv", type: "number" },
      { name: "nominalStrainBreakStdDv", type: "number" },
      { name: "notchedIzodNorme", type: "string" },
      { name: "notchedIzodAv", type: "number" },
      { name: "notchedIzodStdDv", type: "number" },
      { name: "notchedIzodFailureType", type: "string" },
      { name: "unnotchedIzodNorme", type: "string" },
      { name: "unnotchedIzodAv", type: "number" },
      { name: "unnotchedIzodStdDv", type: "number" },
      { name: "unnotchedIzodFailureType", type: "string" },
      { name: "hdtBNorme", type: "string" },
      { name: "hdtBAv", type: "number" },
      { name: "hdtBStdDv", type: "number" },
      { name: "odorNorme", type: "string" },
      { name: "odorNote1", type: "string" },
      { name: "odorNote2", type: "string" },
      { name: "odorNote3", type: "string" },
      { name: "odorAv", type: "number" },
      { name: "odorStdv", type: "number" },
      { name: "vocFogNorme", type: "string" },
      { name: "voc", type: "number" },
      { name: "voc2", type: "number" },
      { name: "fog", type: "number" },
      { name: "l", type: "number" },
      { name: "a", type: "number" },
      { name: "b", type: "number" },
      { name: "cma23Norm", type: "string" },
      { name: "cma23V", type: "number" },
      { name: "cma23NbSamples", type: "number" },
      { name: "cma23MeanBreakType", type: "string" },
      { name: "cma23EnergyForceMaxAv", type: "number" },
      { name: "cma23EnergyForceMaxStdDev", type: "number" },
      { name: "cma23EnergyPunctureAv", type: "number" },
      { name: "cma23EnergyPunctureStdDev", type: "number" },
      { name: "cma0Norm", type: "string" },
      { name: "cma0V", type: "number" },
      { name: "cma0NbSamples", type: "number" },
      { name: "cma0MeanBreakType", type: "string" },
      { name: "cma0EnergyForceMaxAv", type: "number" },
      { name: "cma0EnergyForceMaxStdDev", type: "number" },
      { name: "cma0EnergyPunctureAv", type: "number" },
      { name: "cma0EnergyPunctureStdDev", type: "number" },
      { name: "cma10Norm", type: "string" },
      { name: "cma10V", type: "number" },
      { name: "cma10NbSamples", type: "number" },
      { name: "cma10MeanBreakType", type: "string" },
      { name: "cma10EnergyForceMaxAv", type: "number" },
      { name: "cma10EnergyForceMaxStdDev", type: "number" },
      { name: "cma10EnergyPunctureAv", type: "number" },
      { name: "cma10EnergyPunctureStdDev", type: "number" },
      { name: "cma20Norm", type: "string" },
      { name: "cma20V", type: "number" },
      { name: "cma20NbSamples", type: "number" },
      { name: "cma20MeanBreakType", type: "string" },
      { name: "cma20EnergyForceMaxAv", type: "number" },
      { name: "cma20EnergyForceMaxStdDev", type: "number" },
      { name: "cma20EnergyPunctureAv", type: "number" },
      { name: "cma20EnergyPunctureStdDev", type: "number" },
      { name: "cma30Norm", type: "string" },
      { name: "cma30V", type: "number" },
      { name: "cma30NbSamples", type: "number" },
      { name: "cma30MeanBreakType", type: "string" },
      { name: "cma30EnergyForceMaxAv", type: "number" },
      { name: "cma30EnergyForceMaxStdDev", type: "number" },
      { name: "cma30EnergyPunctureAv", type: "number" },
      { name: "cma30EnergyPunctureStdDev", type: "number" },
      { name: "shredding", type: "string" },
      { name: "overBand", type: "string" },
      { name: "washing", type: "string" },
      { name: "drying", type: "string" },
      { name: "manualSorting", type: "string" },
      { name: "densimetricSorting", type: "string" },
      { name: "opticalSorting", type: "string" },
      { name: "flotation", type: "string" },
      { name: "colorSorting", type: "string" },
      { name: "triboelectricSorting", type: "string" },
      { name: "aeraulicSorting", type: "string" },
      { name: "vacuumTreatmentAlongExtrusionLine", type: "string" },
      { name: "decantation", type: "string" },
      { name: "filtrationOrSize", type: "string" },
      { name: "degasification", type: "string" },
      { name: "homogenization", type: "string" },
      { name: "extrusionLine", type: "string" },
      { name: "lotSizeT", type: "string" },
      { name: "chronology", type: "string" },
    ],
  };

  return familyMaps[family] || [];
}

/**
 * Get the valid property names for a material family (backward compatibility)
 * @param family - The material family
 * @returns Array of valid property names
 */
function getFamilyProperties(family: MaterialFamily): string[] {
  return getFamilyPropertyDefinitions(family).map(definition => definition.name);
}

/**
 * Get the expected type for a property in a material family
 * @param family - The material family
 * @param propertyName - The property name
 * @returns The expected type or undefined if property doesn't exist
 */
function getPropertyType(family: MaterialFamily, propertyName: string): PropertyType | undefined {
  const definitions = getFamilyPropertyDefinitions(family);
  const propertyDefinition = definitions.find(definition => definition.name === propertyName);
  return propertyDefinition?.type ?? undefined;
}

/**
 * Validate the type of a value
 * @param value - The value to validate
 * @param expectedType - The expected type
 * @returns True if the type matches
 */
function validateValueType(value: unknown, expectedType: PropertyType): boolean {
  if (value === null || value === undefined) {
    return true;
  }

  switch (expectedType) {
    case "string": {
      return typeof value === "string";
    }
    case "number": {
      return typeof value === "number" && !Number.isNaN(value);
    }
    case "boolean": {
      return typeof value === "boolean";
    }
    case "date": {
      return value instanceof Date || (typeof value === "string" && !Number.isNaN(Date.parse(value)));
    }
    default: {
      return false;
    }
  }
}

export const propertyValidator = {
  formatInvalidPropertiesMessage(properties: string[], family: MaterialFamily): string {
    if (properties.length === 1) {
      return `Property '${properties[0]}' is not valid for family '${family}'`;
    }

    if (properties.length === 2) {
      return `Property '${properties[0]}' and '${properties[1]}' are not valid for family '${family}'`;
    }

    const lastProperty = properties.at(-1);
    const otherProperties = properties.slice(0, -1).map(property => `'${property}'`).join(", ");
    return `Property ${otherProperties}, and '${lastProperty}' are not valid for family '${family}'`;
  },

  getFamilyProperties,
  getFamilyPropertyDefinitions,
  getPropertyType,

  validateCriteria(family: MaterialFamily, criteria: ValidationCriterion[]): void {
    const invalidProperties: string[] = [];
    const typeErrors: string[] = [];
    const validProperties = propertyValidator.getFamilyProperties(family);

    for (const criterion of criteria) {
      if (criterion.propertyName) {
        if (!validProperties.includes(criterion.propertyName)) {
          invalidProperties.push(criterion.propertyName);
        }
        else if (criterion.value !== undefined) {
          const expectedType = getPropertyType(family, criterion.propertyName);
          if (expectedType && !validateValueType(criterion.value, expectedType)) {
            typeErrors.push(`${criterion.propertyName} must be a ${expectedType} conforming to the specified constraints`);
          }
        }
      }
    }

    const allErrors: string[] = [];

    if (invalidProperties.length > 0) {
      allErrors.push(propertyValidator.formatInvalidPropertiesMessage(invalidProperties, family));
    }

    allErrors.push(...typeErrors);

    if (allErrors.length > 0) {
      throw new BadRequestException([allErrors.join(", ")]);
    }
  },

  validateValueType,
};
