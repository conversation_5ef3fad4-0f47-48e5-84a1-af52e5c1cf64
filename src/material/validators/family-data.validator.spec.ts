import { plainToClass } from "class-transformer";
import { validate } from "class-validator";

import { validateFamilyData } from "./family-data.validator";

import { MaterialFamily } from "@/generated/prisma";

// Test DTO class to apply the validator
class TestMaterialDto {
  public family: MaterialFamily;

  @validateFamilyData()
  public familyData: unknown;
}

describe("ValidateFamilyData", () => {
  describe("Valid family data", () => {
    it("should pass validation for valid POLYMERS family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: {
          comment: "test comment",
          densityAv: 0.95,
          densityNorme: "ISO 1183",
          densityStdDv: 0.01,
          mfiAv: 25.5,
          mfiNorme: "ISO 1133",
          mfiStdDv: 1.2,
          mfiTestConditions: "230°C/2.16kg",
          priceExw: 100.5,
          priceExwDate: "2023-12-01",
          resultUpdateDate: "2023-12-01",
          tds: "test-tds",
          tensileModulusAv: 1500,
          tensileModulusCond: "standard",
          tensileModulusNorme: "ISO 527",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(0);
    });

    it("should pass validation for valid RECYCLE_POLYMERS family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.RECYCLE_POLYMERS,
        familyData: {
          certificatesReachRohs: "yes",
          color: "black",
          materialForm: "pellets",
          pirPcrElv: "test-pir",
          productVolumesKtY: 100,
          resultUpdateDate: "2023-12-01",
          technicalProfileAvgValue: "test-profile",
          volumesAvailableForMactKtY: 50,
          wasteDetails: "test-waste",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(0);
    });

    it("should pass validation for valid FILLERS family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.FILLERS,
        familyData: {
          bet: 45.5,
          codeanonymFiller: "FILLER001",
          d50: 2.5,
          d95d05: 0.8,
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(0);
    });

    it("should pass validation for valid ELASTOMERS family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.ELASTOMERS,
        familyData: {
          codeanonymElasto: "ELAST001",
          density: 1.2,
          mfi190216: 15.5,
          nIzod23: 25,
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(0);
    });

    it("should pass validation for valid ADDITIVES family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.ADDITIVES,
        familyData: {
          anonymizationCode: "ADD001",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(0);
    });

    it("should pass validation when familyData is null or undefined", async () => {
      const testDataNull = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: undefined,
      });

      const testDataUndefined = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: undefined,
      });

      const errorsNull = await validate(testDataNull);
      const errorsUndefined = await validate(testDataUndefined);

      expect(errorsNull).toHaveLength(0);
      expect(errorsUndefined).toHaveLength(0);
    });
  });

  describe("Invalid family data", () => {
    it("should fail validation when family is not specified", async () => {
      const testData = plainToClass(TestMaterialDto, {
        familyData: {
          someProperty: "value",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints).toHaveProperty("ValidateFamilyData");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("Family must be specified");
    });

    it("should fail validation for invalid properties in POLYMERS family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: {
          invalidProperty1: "invalid",
          invalidProperty2: "also invalid",
          validProperty: "test",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints).toHaveProperty("ValidateFamilyData");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("invalidProperty1");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("invalidProperty2");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("POLYMERS");
    });

    it("should fail validation for invalid properties in RECYCLE_POLYMERS family data", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.RECYCLE_POLYMERS,
        familyData: {
          technicalProfileAvgValue: "valid",
          unknownProperty: "invalid",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints).toHaveProperty("ValidateFamilyData");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("unknownProperty");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("RECYCLE_POLYMERS");
    });

    it("should fail validation for invalid property types", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: {
          densityAv: "also should be number",
          mfiAv: "should be number",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints).toHaveProperty("ValidateFamilyData");
      const errorMessage = errors[0].constraints?.ValidateFamilyData;
      expect(errorMessage).toContain("mfiAv");
      expect(errorMessage).toContain("densityAv");
    });

    it("should format single invalid property message correctly", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.FILLERS,
        familyData: {
          invalidProperty: "value",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints?.ValidateFamilyData).toBe(
        "Property 'invalidProperty' is not valid for family 'FILLERS'",
      );
    });

    it("should format two invalid properties message correctly", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.ELASTOMERS,
        familyData: {
          invalidProperty1: "value1",
          invalidProperty2: "value2",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints?.ValidateFamilyData).toBe(
        "Property 'invalidProperty1' and 'invalidProperty2' are not valid for family 'ELASTOMERS'",
      );
    });

    it("should format multiple invalid properties message correctly", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.ADDITIVES,
        familyData: {
          invalidProperty1: "value1",
          invalidProperty2: "value2",
          invalidProperty3: "value3",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints?.ValidateFamilyData).toBe(
        "Property 'invalidProperty1', 'invalidProperty2', and 'invalidProperty3' are not valid for family 'ADDITIVES'",
      );
    });

    it("should fail validation for unknown family type", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: "UNKNOWN_FAMILY" as MaterialFamily,
        familyData: {
          someProperty: "value",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints).toHaveProperty("ValidateFamilyData");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("No validation available for family: UNKNOWN_FAMILY");
    });
  });

  describe("Mixed validation scenarios", () => {
    it("should validate with both valid and invalid properties, showing only invalid ones", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: {
          invalidProp1: "invalid",
          invalidProp2: "also invalid",
          mfiAv: 25.5,
          resultUpdateDate: "2023-12-01",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      const errorMessage = errors[0].constraints?.ValidateFamilyData;
      expect(errorMessage).toContain("invalidProp1");
      expect(errorMessage).toContain("invalidProp2");
      expect(errorMessage).not.toContain("resultUpdateDate");
      expect(errorMessage).not.toContain("mfiAv");
    });

    it("should handle complex validation with inheritance (base polymer properties)", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.RECYCLE_POLYMERS,
        familyData: {
          densityAv: 0.95,
          feedstockOrUseInCompounds: "test-feedstock",
          invalidProperty: "should fail",
          mfiNorme: "ISO 1133",
          resultUpdateDate: "2023-12-01",
          tds: "test-tds",
          technicalProfileAvgValue: "test-profile",
          validationEngineering: "test",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      expect(errors[0].constraints?.ValidateFamilyData).toContain("invalidProperty");
      expect(errors[0].constraints?.ValidateFamilyData).toContain("RECYCLE_POLYMERS");
    });

    it("should validate date format correctly", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: {
          resultUpdateDate: "invalid-date-format",
        },
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(1);
      const errorMessage = errors[0].constraints?.ValidateFamilyData;
      expect(errorMessage).toContain("resultUpdateDate");
      expect(errorMessage).toContain("must be a valid ISO 8601 date string");
    });

    it("should validate empty object as valid", async () => {
      const testData = plainToClass(TestMaterialDto, {
        family: MaterialFamily.POLYMERS,
        familyData: {},
      });

      const errors = await validate(testData);
      expect(errors).toHaveLength(0);
    });
  });
});
