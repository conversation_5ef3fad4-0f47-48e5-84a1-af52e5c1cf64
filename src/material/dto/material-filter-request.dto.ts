export { MATERIAL_FILTER_COLUMN } from "../enum/material-filter-column.enum";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsEnum, IsInt, IsOptional, IsString, Max, Min } from "class-validator";
import { MATERIAL_FILTER_COLUMN } from "../enum/material-filter-column.enum";

export class MaterialFilterRequestDto {
  @ApiProperty({
    description: "Name of the column to query",
    enum: MATERIAL_FILTER_COLUMN,
    example: MATERIAL_FILTER_COLUMN.ORIGIN,
  })
  @IsEnum(MATERIAL_FILTER_COLUMN)
  public column!: MATERIAL_FILTER_COLUMN;

  @ApiPropertyOptional({
    default: 1,
    description: "Page number for pagination",
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  public page?: number = 1;

  @ApiPropertyOptional({
    default: 10,
    description: "Number of items per page",
    example: 10,
    maximum: 100,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  public limit?: number = 10;

  @ApiPropertyOptional({
    description: "Search string for filtering values",
  })
  @IsOptional()
  @IsString()
  @Transform(({ value }: { value: unknown }) => typeof value === "string" ? value.trim() : value)
  public q?: string;
}
