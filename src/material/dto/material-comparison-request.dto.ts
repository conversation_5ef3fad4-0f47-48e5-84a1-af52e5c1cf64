import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>Array, IsOptional, IsString, IsUUID } from "class-validator";

export class MaterialComparisonRequestDto {
  @ApiProperty({
    description: "Array of material IDs to compare",
    example: ["uuid1", "uuid2", "uuid3"],
    maxItems: 10,
    minItems: 2,
    type: [String],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  public materialIds: string[];

  @ApiProperty({
    description: "Include only specific properties in comparison (optional - returns all if not specified)",
    example: ["mfiAv", "densityAv", "stressBreakAv"],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  public includeProperties?: string[];
}
