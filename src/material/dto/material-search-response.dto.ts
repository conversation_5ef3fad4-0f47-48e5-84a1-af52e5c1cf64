import { ApiProperty } from "@nestjs/swagger";
import { MaterialStatus } from "@/generated/prisma";

export class MaterialSearchResponseDto {
  @ApiProperty({
    description: "Material ID",
    example: "f4498fc2-0acd-4a12-b483-055962762479",
  })
  public id: string;

  @ApiProperty({
    description: "Material type",
    example: "Virgin PP Homopolymer",
  })
  public type: string;

  @ApiProperty({
    description: "Material reference",
    example: "A100-80",
  })
  public reference: string;

  @ApiProperty({
    description: "Material family",
    example: "POLYMERS",
  })
  public family: string;

  @ApiProperty({
    description: "Material status",
    enum: MaterialStatus,
    example: MaterialStatus.AVAILABLE,
  })
  public status: MaterialStatus;

  @ApiProperty({
    description: "Supplier name",
    example: "Cibas",
    nullable: true,
    required: false,
  })
  public supplier: string | null;

  @ApiProperty({
    description: "Material origin",
    example: "Asia",
    nullable: true,
    required: false,
  })
  public origin: string | null;

  @ApiProperty({
    description: "Supplier batch number",
    example: "BATCH-12345",
    nullable: true,
    required: false,
  })
  public supplierBatchNumber: string | null;
}
