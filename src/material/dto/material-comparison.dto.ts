import { ApiProperty } from "@nestjs/swagger";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

export class MaterialComparisonDto {
  @ApiProperty({
    description: "Material ID",
    example: "uuid1",
  })
  public id: string;

  @ApiProperty({
    description: "Material reference/name",
    example: "rPP1",
  })
  public reference: string;

  @ApiProperty({
    description: "Material type",
    example: "PP grade 2",
  })
  public type: string;

  @ApiProperty({
    description: "Material family",
    enum: MaterialFamily,
    example: MaterialFamily.POLYMERS,
  })
  public family: MaterialFamily;

  @ApiProperty({
    description: "Material status",
    enum: MaterialStatus,
    example: MaterialStatus.AVAILABLE,
  })
  public status: MaterialStatus;

  @ApiProperty({
    description: "Supplier name",
    example: "Anonymous",
  })
  public supplierName: string;

  @ApiProperty({
    description: "Supplier ID",
    example: "supplier-uuid",
  })
  public supplierId: string;

  @ApiProperty({
    description: "Batch number",
    example: "BCF 3366",
  })
  public supplierBatchNumber: string;

  @ApiProperty({
    description: "Material origin/region",
    example: "Europe",
  })
  public origin: string;

  @ApiProperty({
    description: "Dynamic properties based on material family",
    example: {
      densityAv: 1.2,
      mfiAv: 25,
      stressBreakAv: 30,
    },
  })
  public properties: Record<string, unknown>;
}
