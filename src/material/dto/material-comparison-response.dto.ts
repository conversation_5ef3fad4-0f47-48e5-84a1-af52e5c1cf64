import { ApiProperty } from "@nestjs/swagger";
import { MaterialComparisonDto } from "./material-comparison.dto";

export class MaterialComparisonResponseDto {
  @ApiProperty({
    description: "Array of compared materials with their properties",
    example: ["mfiAv", "densityAv", "stressBreakAv"],
    type: [MaterialComparisonDto],
  })
  public materials: MaterialComparisonDto[];

  @ApiProperty({
    description: "Available properties for this material family",
    example: ["mfiAv", "densityAv", "stressBreakAv"],
    type: [String],
  })
  public availableProperties: string[];

  @ApiProperty({
    description: "Property metadata (units, descriptions, etc.)",
    example: {
      densityAv: { description: "Density Average", unit: "g/cm³" },
      mfiAv: { description: "Melt Flow Index Average", unit: "g/10min" },
    },
  })
  public propertyMetadata: Record<string, unknown>;
}
