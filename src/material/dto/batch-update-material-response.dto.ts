import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { MaterialSearchResponseDto } from "./material-search-response.dto";

export class BatchUpdateMaterialResponseDto {
  @ApiProperty({ description: "Material ID" })
  public id: string;

  @ApiProperty({ description: "Update status", example: "fulfilled" })
  public status: "fulfilled" | "rejected";

  @ApiPropertyOptional({ description: "Updated material data, present if status is fulfilled", type: MaterialSearchResponseDto })
  public value?: MaterialSearchResponseDto;

  @ApiPropertyOptional({ description: "Error reason, present if status is rejected" })
  public reason?: string;
}
