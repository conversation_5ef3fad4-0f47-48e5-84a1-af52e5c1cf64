import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsDateString, IsNumber, IsOptional, IsString } from "class-validator";
import { BasePolymerFamilyDataDto } from "./base-polymer.dto";

export class RecyclePolymerFamilyDataDto extends BasePolymerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public technicalProfileAvgValue?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public color?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public pirPcrElv?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public wasteDetails?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public materialForm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public productVolumesKtY?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public volumesAvailableForMactKtY?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public certificatesReachRohs?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public endOfWasteStatus?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public filtrationLocation?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public systemFiltration?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public filtrationSize?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public quantityFilteredRemaining?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d22NbFiltersUsed?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d22NbFilterPerKgFeedstock?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d32QuantityScrap?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d32NbFilterPerKgFeedstock?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public levelOfPollution?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public venting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  public trialDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public vacuumPressure?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public screwSpeed?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public screwProfile?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public ashContentNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public ashContentAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public ashContentStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public odorNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public odorNote1?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public odorNote2?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public odorNote3?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public odorAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public odorStdv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public vocFogNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public voc?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public voc2?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public fog?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public l?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public a?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public b?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public tensileModulusConditions?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public shredding?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public overBand?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public washing?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public drying?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public manualSorting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public densimetricSorting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public opticalSorting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public flotation?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public colorSorting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public triboelectricSorting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public aeraulicSorting?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public vacuumTreatmentAlongExtrusionLine?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public decantation?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public filtrationOrSize?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public degasification?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public homogenization?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public extrusionLine?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public lotSizeT?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public chronology?: string;
}
