import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsUUID } from "class-validator";

export class AdditiveFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  public id?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  public materialId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public anonymizationCode?: string;
}
