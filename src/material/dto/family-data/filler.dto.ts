import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsN<PERSON>ber, IsOptional, IsString, IsUUID } from "class-validator";

export class FillerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  public id?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  public materialId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public codeanonymFiller?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public bet?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d50?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public d95d05?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public whiteness?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public form?: string;
}
