import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsDateString, IsNumber, IsOptional, IsString, IsUUID } from "class-validator";

export class BasePolymerFamilyDataDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  public id?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  public materialId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  public resultUpdateDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  public validationEngineeringAndFeedstockOrUseInCompounds?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public tds?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public priceExw?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDateString()
  public priceExwDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public comment?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public mfiNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public mfiTestConditions?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public mfiAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public mfiStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public densityNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public densityAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public densityStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public tensileModulusNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public tensileModulusAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public tensileModulusStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public flexuralModulusNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralModulusAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralModulusStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralStressFcAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public flexuralStressFcStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public stressBreakNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressBreakAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressBreakStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressYieldAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public stressYieldStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public yieldStrainAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public yieldStrainStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public strainBreakNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public strainBreakAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public strainBreakStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public nominalStrainBreakAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public nominalStrainBreakStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public notchedIzodNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public notchedIzodAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public notchedIzodStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public notchedIzodFailureType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public unnotchedIzodNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public unnotchedIzodAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public unnotchedIzodStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public unnotchedIzodFailureType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public hdtBNorme?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public hdtBAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public hdtBStdDv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma23Norm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma23V?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma23NbSamples?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma23MeanBreakType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma23EnergyForceMaxAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma23EnergyForceMaxStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma23EnergyPunctureAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma23EnergyPunctureStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma0Norm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma0V?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma0NbSamples?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma0MeanBreakType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma0EnergyForceMaxAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma0EnergyForceMaxStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma0EnergyPunctureAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma0EnergyPunctureStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma10Norm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma10V?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma10NbSamples?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma10MeanBreakType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma10EnergyForceMaxAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma10EnergyForceMaxStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma10EnergyPunctureAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma10EnergyPunctureStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma20Norm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma20V?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma20NbSamples?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma20MeanBreakType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma20EnergyForceMaxAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma20EnergyForceMaxStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma20EnergyPunctureAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma20EnergyPunctureStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma30Norm?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma30V?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma30NbSamples?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  public cma30MeanBreakType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma30EnergyForceMaxAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma30EnergyForceMaxStdDev?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma30EnergyPunctureAv?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  public cma30EnergyPunctureStdDev?: number;
}
