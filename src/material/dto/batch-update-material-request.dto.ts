import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsObject, IsOptional, IsString } from "class-validator";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

export class BatchUpdateMaterialRequestDto {
  @ApiProperty({ description: "Material ID", example: "material-uuid" })
  @IsString()
  public id: string;

  @ApiPropertyOptional({ description: "Material type", example: "Polypropylene" })
  @IsOptional()
  @IsString()
  public type?: string;

  @ApiPropertyOptional({ description: "Material reference code", example: "MAT-001" })
  @IsOptional()
  @IsString()
  public reference?: string;

  @ApiProperty({ description: "Material family", enum: MaterialFamily, example: MaterialFamily.POLYMERS })
  @IsEnum(MaterialFamily)
  public family: MaterialFamily;

  @ApiPropertyOptional({ description: "Supplier", example: "IG 8-01" })
  @IsOptional()
  @IsString()
  public supplier?: string;

  @ApiPropertyOptional({ description: "Supplier ID", example: "supplier-uuid" })
  @IsOptional()
  @IsString()
  public supplierId?: string;

  @ApiProperty({ description: "Supplier batch number", example: "BATCH-123" })
  @IsString()
  public supplierBatchNumber: string;

  @ApiPropertyOptional({ description: "Material origin", example: "Europe" })
  @IsOptional()
  @IsString()
  public origin?: string;

  @ApiPropertyOptional({ description: "Material status", enum: MaterialStatus, example: MaterialStatus.AVAILABLE })
  @IsOptional()
  @IsEnum(MaterialStatus)
  public status?: MaterialStatus;

  @ApiPropertyOptional({ description: "Family-specific data (polymer, filler, elastomer, additive, or recyclePolymer fields)", example: { densityAv: 1.2, mfiAv: 1 } })
  @IsOptional()
  @IsObject()
  public familyData?: Record<string, unknown>;
}
