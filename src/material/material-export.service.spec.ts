import { Test, TestingModule } from "@nestjs/testing";
import * as exceljs from "exceljs";
import { MaterialExportRequestDto } from "./dto/material-export-request.dto";
import { MaterialExportService } from "./material-export.service";
import { MaterialService } from "./material.service";
import { MaterialRepository } from "./repositories/material.repository";
import { MaterialWithFamilyData } from "./index";
import { CriteriaOperator } from "@/common/dto/criteria-operator.dto";
import { MaterialFamily, MaterialStatus } from "@/generated/prisma";

const mockMaterialRepository = {
  findWithFamilyFieldFilters: jest.fn(),
};

const mockMaterialService = {
  mapToMaterialPageResponse: jest.fn(),
};

describe("MaterialExportService", () => {
  let service: MaterialExportService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MaterialExportService,
        {
          provide: MaterialRepository,
          useValue: mockMaterialRepository,
        },
        {
          provide: MaterialService,
          useValue: mockMaterialService,
        },
      ],
    }).compile();

    service = module.get<MaterialExportService>(MaterialExportService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("exportMaterials", () => {
    const mockMaterial: MaterialWithFamilyData = {
      family: MaterialFamily.POLYMERS,
      id: "material-1",
      origin: "Europe",
      polymer: {
        densityAv: 0.905,
        id: "polymer-1",
        materialId: "material-1",
        mfiAv: 22.5,
        tensileModulusAv: 1300,
      },
      reference: "PP-H 1200",
      status: MaterialStatus.AVAILABLE,
      supplier: { id: "supplier-1", name: "Test Supplier" },
      supplierBatchNumber: "BATCH-001",
      type: "Virgin PP Homopolymer",
    } as MaterialWithFamilyData;

    const mockFormattedMaterial = {
      densityAv: 0.905,
      family: "POLYMERS",
      familyData: {
        densityAv: 0.905,
        mfiAv: 22.5,
        tensileModulusAv: 1300,
      },
      id: "material-1",
      mfiAv: 22.5,
      origin: "Europe",
      reference: "PP-H 1200",
      status: "AVAILABLE",
      supplier: "Test Supplier",
      supplierBatchNumber: "BATCH-001",
      tensileModulusAv: 1300,
      type: "Virgin PP Homopolymer",
    };

    beforeEach(() => {
      mockMaterialRepository.findWithFamilyFieldFilters.mockResolvedValue({
        data: [mockMaterial],
        total: 1,
      });
      mockMaterialService.mapToMaterialPageResponse.mockReturnValue(mockFormattedMaterial);
    });

    it("should export materials with selected fields", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: ["id", "reference", "mfiAv"],
      };

      const result = await service.exportMaterials(requestDto);

      expect(result).toBeInstanceOf(Buffer);
      expect(mockMaterialRepository.findWithFamilyFieldFilters).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        familyFilters: [],
        selectedFields: ["id", "reference", "mfiAv"],
      });
      expect(mockMaterialService.mapToMaterialPageResponse).toHaveBeenCalledWith(mockMaterial);
    });

    it("should export all available fields when selectedFields is empty", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: [],
      };

      const result = await service.exportMaterials(requestDto);

      expect(result).toBeInstanceOf(Buffer);
      expect(mockMaterialRepository.findWithFamilyFieldFilters).toHaveBeenCalled();
      expect(mockMaterialService.mapToMaterialPageResponse).toHaveBeenCalledWith(mockMaterial);
    });

    it("should handle empty materials data", async () => {
      mockMaterialRepository.findWithFamilyFieldFilters.mockResolvedValue({
        data: [],
        total: 0,
      });

      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: ["id", "reference"],
      };

      const result = await service.exportMaterials(requestDto);

      expect(result).toBeInstanceOf(Buffer);
    });

    it("should create workbook with correct structure", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: ["id", "reference", "mfiAv"],
      };

      const result = await service.exportMaterials(requestDto);

      expect(result).toBeInstanceOf(Buffer);
      expect(result.byteLength).toBeGreaterThan(0);

      const workbook = new exceljs.Workbook();

      await workbook.xlsx.load(result);

      const worksheet = workbook.getWorksheet("Materials POLYMERS");
      expect(worksheet).toBeDefined();

      const headerRow = worksheet?.getRow(1);
      expect(headerRow?.getCell(2).value).toBe("Product Reference");

      const defaultRow = worksheet?.getRow(2);
      expect(defaultRow?.getCell(1).value).toBe("by default");
      expect(defaultRow?.getCell(2).value).toBe("by default");

      const typeRow = worksheet?.getRow(3);
      expect(typeRow?.getCell(1).value).toBe("Text");
      expect(typeRow?.getCell(2).value).toBe("Text");

      const dataRow = worksheet?.getRow(4);
      expect(dataRow?.getCell(1).value).toBe("material-1");
      expect(dataRow?.getCell(2).value).toBe("PP-H 1200");
    });

    it("should handle family criteria filtering", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 20,
          },
        ],
        selectedFields: ["id", "reference"],
      };

      await service.exportMaterials(requestDto);

      expect(mockMaterialRepository.findWithFamilyFieldFilters).toHaveBeenCalledWith({
        family: MaterialFamily.POLYMERS,
        familyCriteria: [
          {
            operator: ">=",
            propertyName: "mfiAv",
            value: 20,
          },
        ],
        familyFilters: [
          {
            operator: CriteriaOperator.GREATER_THAN_OR_EQUAL,
            propertyName: "mfiAv",
            value: 20,
          },
        ],
        selectedFields: ["id", "reference"],
      });
    });
  });

  describe("getFieldType functionality", () => {
    it("should return correct types when creating workbook", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: ["id", "reference", "mfiAv"],
      };

      const result = await service.exportMaterials(requestDto);

      const workbook = new exceljs.Workbook();
      await workbook.xlsx.load(result);

      const worksheet = workbook.getWorksheet("Materials POLYMERS");
      const typeRow = worksheet?.getRow(3);

      expect(typeRow?.getCell(1).value).toBe("Text");
      expect(typeRow?.getCell(2).value).toBe("Text");
    });
  });

  describe("getAllAvailableFields functionality", () => {
    it("should return all available fields when no fields specified", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: [],
      };

      const result = await service.exportMaterials(requestDto);

      expect(result).toBeInstanceOf(Buffer);
      expect(mockMaterialService.mapToMaterialPageResponse).toHaveBeenCalled();
    });
  });

  describe("data formatting functionality", () => {
    it("should format materials correctly for export", async () => {
      const requestDto: MaterialExportRequestDto = {
        family: MaterialFamily.POLYMERS,
        familyCriteria: [],
        selectedFields: ["id", "reference", "mfiAv"],
      };

      const result = await service.exportMaterials(requestDto);

      const workbook = new exceljs.Workbook();
      await workbook.xlsx.load(result);

      const worksheet = workbook.getWorksheet("Materials POLYMERS");
      const dataRow = worksheet?.getRow(4);

      expect(dataRow?.getCell(1).value).toBe("material-1");
      expect(dataRow?.getCell(2).value).toBe("PP-H 1200");
    });
  });
});
