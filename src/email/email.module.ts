import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { EMAIL_ENABLED_TOKEN } from "./email.constants";
import { EmailService } from "./email.service";

@Module({
  exports: [EmailService],
  imports: [ConfigModule],
  providers: [
    EmailService,
    {
      inject: [ConfigService],
      provide: EMAIL_ENABLED_TOKEN,
      useFactory: (configService: ConfigService) => {
        return configService.get<boolean>("EMAIL_ENABLED", false);
      },
    },
  ],
})
export class EmailModule {}
