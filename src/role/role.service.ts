import { Injectable, NotFoundException } from "@nestjs/common";
import { RoleResponseDto } from "./dto";
import { RoleRepository } from "./repositories";

@Injectable()
export class RoleService {
  public constructor(private readonly roleRepository: RoleRepository) {}

  public async findOne(id: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findById(id);

    if (!role) {
      throw new NotFoundException("Role not found");
    }

    return role;
  }

  public async findOneOrThrow(id: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findById(id);

    if (!role) {
      throw new NotFoundException(`Role with ID '${id}' not found`);
    }

    return role;
  }

  public async getRoleNames(): Promise<string[]> {
    return this.roleRepository.getRoleNames();
  }

  public async findAll(): Promise<RoleResponseDto[]> {
    return this.roleRepository.findAll();
  }

  public async findByCode(code: string): Promise<RoleResponseDto> {
    const role = await this.roleRepository.findByCode(code);

    if (!role) {
      throw new NotFoundException(`Role with code '${code}' not found`);
    }

    return role;
  }
}
