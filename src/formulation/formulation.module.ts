import { Module } from "@nestjs/common";
import { AuthModule } from "../auth/auth.module";
import { PrismaService } from "../prisma.service";
import { ComparisonService } from "./comparison.service";
import { FormulationController } from "./formulation.controller";
import { FormulationService } from "./formulation.service";
import { ComparisonRepository } from "./repositories/comparison.repository";
import { FormulationRepository } from "./repositories/formulation.repository";
import { TestResultRepository } from "./repositories/test-result.repository";
import { MaterialModule } from "@/material";

@Module({
  controllers: [FormulationController],
  exports: [FormulationService, FormulationRepository, ComparisonService, ComparisonRepository],
  imports: [AuthModule, MaterialModule],
  providers: [
    FormulationService,
    FormulationRepository,
    ComparisonService,
    ComparisonRepository,
    TestResultRepository,
    PrismaService,
  ],
})
export class FormulationModule {}
