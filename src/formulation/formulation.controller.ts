import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  Request,
  UseGuards,
} from "@nestjs/common";
import {
  ApiOAuth2,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { AuthGuard } from "../auth/auth.guard";
import { RoleGuard, ROLES } from "../auth/role.guard";
import { AuthenticatedRequest } from "../common";
import { UserRole } from "../role/role.types";
import { ComparisonService } from "./comparison.service";
import {
  ComparisonRequestDto,
  ComparisonResponseDto,
  CreateFormulationDto,
  CreateFormulationResponseDto,
  CreateSimulationDto,
  CreateSimulationResponseDto,
  FormulationFilterDto,
  FormulationResponseDto,
  PaginatedFormulationResponseDto,
} from "./dto";
import { FormulationService } from "./formulation.service";

@ApiOAuth2([process.env.AZURE_API_SCOPE ?? "default-scope"])
@UseGuards(AuthGuard, RoleGuard)
@ApiTags("formulations")
@Controller("formulations")
export class FormulationController {
  public constructor(
    private readonly formulationService: FormulationService,
    private readonly comparisonService: ComparisonService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    description:
      "Create multiple formulations using a compact format. Materials and test results are organized by grades as key-value pairs to eliminate redundancy. Each material specifies its percentage for each grade, and test results specify values for each grade. Only materials with non-zero percentages are included in each formulation. All materials must exist in the database. Access is role-based: Admin, Data Scientist, Engineering Manager, and Engineers can create formulations.",
    summary: "Create multiple formulations with materials and test results",
  })
  @ApiResponse({
    description: "Formulations created successfully. Each grade (A, B, C, D, E) becomes a separate formulation with only non-zero percentage materials included.",
    status: HttpStatus.CREATED,
    type: CreateFormulationResponseDto,
  })
  @ApiResponse({
    description: "Bad request - Invalid data or materials not found",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async createFormulations(
    @Body() createFormulationDto: CreateFormulationDto,
    @Request() request: AuthenticatedRequest,
  ): Promise<CreateFormulationResponseDto> {
    const userId = request.user?.oid ?? (Array.isArray(request.headers?.oid) ? request.headers?.oid[0] : request.headers?.oid);

    if (!userId) {
      throw new Error("User ID not found in request");
    }

    return await this.formulationService.createFormulations(
      createFormulationDto,
      userId,
    );
  }

  @Get()
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    description: "Get paginated list of formulations with optional filters. Supports search across name and grade fields, individual name/grade filtering, and ownerId filtering. Supports pagination. Engineers can only see formulations they own or have approved access requests.",
    summary: "Get formulations with filtering",
  })
  @ApiResponse({
    description: "Formulations retrieved successfully",
    status: HttpStatus.OK,
    type: PaginatedFormulationResponseDto,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async getFormulations(
    @Query() filter: FormulationFilterDto,
    @Req() request?: AuthenticatedRequest,
  ): Promise<PaginatedFormulationResponseDto> {
    const userRole = request?.user?.role;
    const userId = request?.user?.oid;

    return this.formulationService.getFormulations(filter, userRole, userId);
  }

  @Get(":id")
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    description: "Retrieve a single formulation by its unique ID. Access is role-based: Admin, Data Scientist, Engineering Manager, and Engineers can view formulations.",
    summary: "Get a single formulation by ID",
  })
  @ApiParam({ description: "Formulation ID", name: "id", type: String })
  @ApiResponse({
    description: "Formulation retrieved successfully",
    status: HttpStatus.OK,
    type: FormulationResponseDto,
  })
  @ApiResponse({
    description: "Formulation not found",
    status: HttpStatus.NOT_FOUND,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public async getFormulationById(
    @Param("id") id: string,
    @Req() request?: AuthenticatedRequest,
  ): Promise<FormulationResponseDto> {
    const userRole = request?.user?.role;
    const userId = request?.user?.oid;

    const formulation = await this.formulationService.getFormulationById(id, userRole, userId);
    if (!formulation) {
      throw new NotFoundException(`Formulation with ID ${id} not found`);
    }
    return formulation;
  }

  @Post("compare")
  @HttpCode(200)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    description:
      "Compare materials based on formulations. Returns formulations sorted by matching material count with their test results. Input materialId array is optional. Materials that were searched will be marked with 'searched: true'. Each formulation includes test results from the testResults array. Access is role-based: Admin, Data Scientist, and Engineering Manager have full access. Engineers have conditional access. Feedstock & Recycling Members and Material Manager are restricted from this endpoint.",
    summary: "Compare materials based on formulations",
  })
  @ApiResponse({
    description: "Material comparisons retrieved successfully",
    status: 200,
    type: ComparisonResponseDto,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: 401,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: 403,
  })
  public async compareFormulations(
    @Body() request: ComparisonRequestDto,
    @Request() requestAuth: AuthenticatedRequest,
  ): Promise<ComparisonResponseDto> {
    const userRole = requestAuth.user?.role ?? (Array.isArray(requestAuth.headers?.role) ? requestAuth.headers?.role[0] : requestAuth.headers?.role);
    const userId = requestAuth.user?.oid ?? (Array.isArray(requestAuth.headers?.oid) ? requestAuth.headers?.oid[0] : requestAuth.headers?.oid);

    if (!userId || !userRole) {
      throw new Error("User ID or role not found in request");
    }

    request.criteria?.map((c) => {
      if (c.valueTo) {
        c.value = [Number(c.value), c.valueTo];
      }
    });

    return this.comparisonService.getComparisons(request, { userId, userRole });
  }

  @Post("simulation")
  @HttpCode(HttpStatus.OK)
  @ROLES(
    UserRole.ADMIN,
    UserRole.DATA_SCIENTIST,
    UserRole.ENGINEERING_MANAGER,
    UserRole.ENGINEERS,
  )
  @ApiOperation({
    description: "Simulate test results for a formulation based on input materials and grades. Returns mock results for demonstration.",
    summary: "Simulate formulation test results",
  })
  @ApiResponse({
    description: "Simulation results returned.",
    status: HttpStatus.OK,
    type: CreateSimulationResponseDto,
  })
  @ApiResponse({
    description: "Bad request - Invalid data or materials not found",
    status: HttpStatus.BAD_REQUEST,
  })
  @ApiResponse({
    description: "Unauthorized - Invalid or missing token",
    status: HttpStatus.UNAUTHORIZED,
  })
  @ApiResponse({
    description: "Forbidden - Insufficient permissions for your role",
    status: HttpStatus.FORBIDDEN,
  })
  public simulateFormulation(
    @Body() createSimulationDto: CreateSimulationDto,
  ): Promise<CreateSimulationResponseDto> {
    return this.formulationService.simulateFormulation(createSimulationDto);
  }
}
