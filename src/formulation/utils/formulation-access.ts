import { ACCESS_STATUS } from "@/formulation/enum/access-status.enum";
import { RequestStatus } from "@/generated/prisma";
import { UserRole } from "@/role/role.types";

export class FormulationAccess {
  /**
   * Returns array of user roles that have full privileged access to formulations
   * @returns Array of privileged user roles
   */
  public static getFullPrivilegedRoles(): UserRole[] {
    return [
      UserRole.ADMIN,
      UserRole.DATA_SCIENTIST,
      UserRole.ENGINEERING_MANAGER,
    ];
  }

  /**
   * Maps request status to corresponding access status
   * @param requestStatus The request status to map
   * @returns Corresponding ACCESS_STATUS enum value
   */
  public static mapRequestStatusToAccessStatus(requestStatus: string): ACCESS_STATUS {
    switch (requestStatus) {
      case RequestStatus.PENDING_APPROVAL: {
        return ACCESS_STATUS.PENDING;
      }
      case RequestStatus.APPROVED: {
        return ACCESS_STATUS.ACCESSED;
      }
      case RequestStatus.REJECTED: {
        return ACCESS_STATUS.REJECTED;
      }
      default: {
        return ACCESS_STATUS.REQUIRED;
      }
    }
  }

  /**
   * Checks if user has privileged role that grants full access
   * @param userRole The user role to check
   * @returns True if user has privileged role, false otherwise
   */
  public static hasFullAccessRole(userRole?: string): boolean {
    return userRole ? this.getFullPrivilegedRoles().includes(userRole as UserRole) : false;
  }

  /**
   * Determines if user is owner of the formulation
   * @param formulationOwnerId The owner ID of the formulation
   * @param userId The user ID to check ownership against
   * @returns True if user is owner, false otherwise
   */
  public static isFormulationOwner(formulationOwnerId: string, userId?: string): boolean {
    return Boolean(userId && formulationOwnerId === userId);
  }
}
