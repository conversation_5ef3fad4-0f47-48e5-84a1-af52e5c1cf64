import { ApiProperty } from "@nestjs/swagger";

export class CreatedFormulationDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "uuid-1",
  })
  public id: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper Formulation",
  })
  public name: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  public grade: string;

  @ApiProperty({
    description: "Owner ID",
    example: "user-uuid",
  })
  public ownerId: string;
}
