import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";

export class FormulationTestResultDto {
  @ApiProperty({
    description: "Test result ID",
    example: "test-result-1",
  })
  public id: string;

  @ApiProperty({
    description: "Test name",
    example: "Tensile Strength",
  })
  public testName: string;

  @ApiProperty({
    description: "Test standard",
    example: "ASTM D638",
    nullable: true,
    required: false,
  })
  @Transform(({ value }: { value: string | null }) => value ?? undefined)
  public standard?: string;

  @ApiProperty({
    description: "Test condition",
    example: "23°C, 50% RH",
    nullable: true,
    required: false,
  })
  @Transform(({ value }: { value: string | null }) => value ?? undefined)
  public condition?: string;

  @ApiProperty({
    description: "Test value",
    example: 25.5,
  })
  public value: number;

  @ApiProperty({
    description: "Minimum range",
    example: 20,
    nullable: true,
    required: false,
  })
  @Transform(({ value }: { value: number | null }) => value ?? undefined)
  public minRange?: number;

  @ApiProperty({
    description: "Maximum range",
    example: 30,
    nullable: true,
    required: false,
  })
  @Transform(({ value }: { value: number | null }) => value ?? undefined)
  public maxRange?: number;
}
