import { ApiProperty } from "@nestjs/swagger";
import { ResponseSimulationMaterialDto } from "./simulation/response-simulation-material.dto";
import { ResponseSimulationTestResultDto } from "./simulation/response-simulation-test-result.dto";

export class CreateSimulationResponseDto {
  @ApiProperty({ description: "Materials for simulation", type: [ResponseSimulationMaterialDto] })
  public materials: ResponseSimulationMaterialDto[];

  @ApiProperty({ description: "Array of test results for the simulation", type: [ResponseSimulationTestResultDto] })
  public simulationResults: ResponseSimulationTestResultDto[];

  @ApiProperty({ description: "Array of test results for the simulation", type: [ResponseSimulationTestResultDto] })
  public real: ResponseSimulationTestResultDto[];
}
