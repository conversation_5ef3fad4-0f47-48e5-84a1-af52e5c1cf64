import { ApiProperty } from "@nestjs/swagger";
import { CreatedFormulationDto } from "./created-formulation.dto";

export class CreateFormulationResponseDto {
  @ApiProperty({
    description: "Array of created formulations - one for each grade from the compact format",
    example: [
      {
        grade: "A",
        id: "f1234567-89ab-cdef-0123-456789abcdef",
        name: "Bumper Formulation",
        ownerId: "user-uuid-123",
      },
      {
        grade: "B",
        id: "f2345678-9abc-def0-1234-56789abcdef0",
        name: "Bumper Formulation",
        ownerId: "user-uuid-123",
      },
      {
        grade: "C",
        id: "f3456789-abcd-ef01-2345-6789abcdef01",
        name: "Bumper Formulation",
        ownerId: "user-uuid-123",
      },
      {
        grade: "D",
        id: "f4567890-bcde-f012-3456-789abcdef012",
        name: "Bumper Formulation",
        ownerId: "user-uuid-123",
      },
      {
        grade: "E",
        id: "f5678901-cdef-0123-4567-89abcdef0123",
        name: "Bumper Formulation",
        ownerId: "user-uuid-123",
      },
    ],
    type: [CreatedFormulationDto],
  })
  public data: CreatedFormulationDto[];
}
