import { ApiProperty } from "@nestjs/swagger";
import { IsObject, IsUUID } from "class-validator";

export class CreateFormulationMaterialDto {
  @ApiProperty({
    description: "Material ID",
    example: "12253e96-c065-4a19-9904-9028d5eec93a",
  })
  @IsUUID()
  public materialId: string;

  @ApiProperty({
    description: "Percentage of material per grade (grade as key, percentage as value)",
    example: { a: 20, b: 25, c: 0, d: 0, e: 15 },
  })
  @IsObject()
  public grades: Record<string, number>;
}
