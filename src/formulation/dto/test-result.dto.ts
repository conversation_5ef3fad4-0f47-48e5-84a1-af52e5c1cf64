import { ApiProperty } from "@nestjs/swagger";

export class TestResultDto {
  @ApiProperty({
    description: "Test result ID",
    example: "12345678-1234-1234-1234-123456789abc",
  })
  public id: string;

  @ApiProperty({
    description: "Name of the test performed",
    example: "Tensile Strength",
  })
  public testName: string;

  @ApiProperty({
    description: "Test standard used",
    example: "ASTM D638",
    required: false,
  })
  public standard: string | null;

  @ApiProperty({
    description: "Test condition",
    example: "23°C, 50% RH",
    required: false,
  })
  public condition: string | null;

  @ApiProperty({
    description: "Test result value",
    example: 25.5,
  })
  public value: number;

  @ApiProperty({
    description: "Minimum acceptable range",
    example: 20,
    required: false,
  })
  public minRange?: number | null;

  @ApiProperty({
    description: "Maximum acceptable range",
    example: 30,
    required: false,
  })
  public maxRange?: number | null;
}
