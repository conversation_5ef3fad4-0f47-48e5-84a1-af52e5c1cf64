import { ApiProperty } from "@nestjs/swagger";
import { PaginationMetaDto } from "../../common/dto/pagination-meta.dto";
import { FormulationResponseDto } from "./formulation-response.dto";

export class PaginatedFormulationResponseDto {
  @ApiProperty({
    description: "List of formulations",
    type: [FormulationResponseDto],
  })
  public data: FormulationResponseDto[];

  @ApiProperty({
    description: "Pagination metadata",
    type: PaginationMetaDto,
  })
  public meta: PaginationMetaDto;
}
