import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional, IsString, IsN<PERSON>ber, Min, Max } from "class-validator";

export class FormulationFilterDto {
  @ApiPropertyOptional({
    description: "Search across formulation name and grade fields",
  })
  @IsOptional()
  @IsString()
  public search?: string;

  @ApiPropertyOptional({
    description: "Filter by formulation name",
  })
  @IsOptional()
  @IsString()
  public name?: string;

  @ApiPropertyOptional({
    description: "Filter by formulation grade",
  })
  @IsOptional()
  @IsString()
  public grade?: string;

  @ApiPropertyOptional({
    description: "Filter by owner ID",
  })
  @IsOptional()
  @IsString()
  public ownerId?: string;

  @ApiPropertyOptional({
    default: 1,
    description: "Page number",
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  public page?: number = 1;

  @ApiPropertyOptional({
    default: 10,
    description: "Items per page",
    example: 10,
    maximum: 100,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  public limit?: number = 10;
}
