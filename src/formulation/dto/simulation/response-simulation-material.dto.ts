import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";
import { BaseSimulationMaterialDto } from "./base-simulation-material.dto";

export class ResponseSimulationMaterialDto extends BaseSimulationMaterialDto {
  @ApiProperty({ description: "Material type", example: "Virgin PP Homopolymer" })
  @IsOptional()
  @IsString()
  public materialType?: string;

  @ApiProperty({ description: "Material reference", example: "PP-H 1200" })
  @IsOptional()
  @IsString()
  public reference?: string;
}
