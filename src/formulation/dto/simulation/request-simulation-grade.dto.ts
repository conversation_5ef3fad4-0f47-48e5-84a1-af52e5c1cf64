import { ApiProperty } from "@nestjs/swagger";
import { IsN<PERSON>ber, IsOptional, IsString } from "class-validator";

export class RequestSimulationGradeDto {
  @ApiProperty({ description: "Formulation ID", example: "b382fbab-ef7b-4fb2-b85d-be5dfe3eb4e1" })
  @IsOptional()
  @IsString()
  public formulationId: string;

  @ApiProperty({ description: "Code", example: "BMP-001" })
  @IsOptional()
  @IsString()
  public code: string;

  @ApiProperty({ description: "Grade letter", example: "A" })
  @IsOptional()
  @IsString()
  public grade: string;

  @ApiProperty({ description: "Grade value", example: 20 })
  @IsOptional()
  @IsNumber()
  public value: number;
}
