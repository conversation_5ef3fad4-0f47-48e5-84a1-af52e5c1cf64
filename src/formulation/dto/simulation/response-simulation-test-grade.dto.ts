import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";

export class ResponseSimulationTestGradeDto {
  @ApiProperty({ description: "Grade letter", example: "A" })
  @IsOptional()
  @IsString()
  public grade: string;

  @ApiProperty({ description: "Grade value", example: 1200 })
  @IsOptional()
  public value: number;

  @ApiProperty({ description: "Minimum range for grade", example: 1100 })
  @IsOptional()
  public minRange: number;

  @ApiProperty({ description: "Maximum range for grade", example: 1380 })
  @IsOptional()
  public maxRange: number;
}
