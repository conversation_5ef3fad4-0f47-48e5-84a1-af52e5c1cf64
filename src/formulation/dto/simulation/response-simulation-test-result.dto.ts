import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";
import { ResponseSimulationTestGradeDto } from "./response-simulation-test-grade.dto";

export class ResponseSimulationTestResultDto {
  @ApiProperty({ description: "Test result ID", example: "test-001" })
  @IsOptional()
  @IsString()
  public id: string;

  @ApiProperty({ description: "Test name", example: "Tensile modulus" })
  @IsOptional()
  @IsString()
  public testName: string;

  @ApiProperty({ description: "Test standard", example: "ISO 527-2" })
  @IsOptional()
  @IsString()
  public standard: string;

  @ApiProperty({ description: "Test condition", example: "at 23°C" })
  @IsOptional()
  @IsString()
  public condition: string;

  @ApiProperty({ description: "Grades for this test result", type: [ResponseSimulationTestGradeDto] })
  @IsOptional()
  public grades: ResponseSimulationTestGradeDto[];
}
