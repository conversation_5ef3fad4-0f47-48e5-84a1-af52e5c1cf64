import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";
import { RequestSimulationGradeDto } from "./request-simulation-grade.dto";

export abstract class BaseSimulationMaterialDto {
  @ApiProperty({ description: "Material ID", example: "f4d16359-abe9-4359-a58f-00723b8aa0ee" })
  @IsString()
  public materialId: string;

  @ApiProperty({ description: "Grades for this material", type: [RequestSimulationGradeDto] })
  @IsOptional()
  public grades: RequestSimulationGradeDto[];
}
