import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsBoolean, IsEnum, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { CRITERIA_OPERATOR } from "@/common/dto/criteria-operator.dto";

export class CriteriaDto {
  @ApiProperty({
    description: "Property name that matches specification property",
    example: "mfi",
  })
  @IsString()
  public propertyName: string;

  @ApiProperty({
    description: "Tier/priority level for optional criteria only (1 = highest priority, 2 = second priority, etc.)",
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? Number(value) : undefined)
  public tier?: number;

  @ApiProperty({
    description: "Comparison operator",
    enum: CRITERIA_OPERATOR,
    example: CRITERIA_OPERATOR.GREATER_THAN_OR_EQUAL,
  })
  @IsEnum(CRITERIA_OPERATOR)
  public operator: CRITERIA_OPERATOR;

  @ApiProperty({
    description: "Value",
    example: 5,
  })
  public value: number | [number, number];

  @ApiProperty({
    description: "Value",
    example: 5,
  })
  public valueTo: number;

  @ApiProperty({
    description: "Whether this criteria is required (true) or optional (false)",
    example: true,
  })
  @IsBoolean()
  public required: boolean;
}
