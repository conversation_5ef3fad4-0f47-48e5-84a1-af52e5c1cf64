import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsString, ValidateNested } from "class-validator";
import { CreateFormulationMaterialDto } from "./create-formulation-material.dto";
import { CreateFormulationTestResultDto } from "./create-formulation-test-result.dto";

export class CreateFormulationDto {
  @ApiProperty({
    description: "Code for the formulation, typically a unique identifier",
    example: "EX-1234",
  })
  public code: string;

  @ApiProperty({
    description: "Common name for all formulations",
    example: "Bumper Formulation",
  })
  @IsString()
  public name: string;

  @ApiProperty({
    description: "Array of materials with their percentages per grade",
    example: [
      {
        grades: { a: 20 },
        materialId: "cf41949a-a3f8-4783-ae64-17e2d19bf2f2",
      },
      {
        grades: { a: 20 },
        materialId: "c4cfe815-9a20-45f6-b2ce-b8bcf1921e6d",
      },
      {
        grades: { a: 40 },
        materialId: "61f4570d-c25e-4142-8749-f269e70abcc2",
      },
    ],
    type: [CreateFormulationMaterialDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateFormulationMaterialDto)
  public materials: CreateFormulationMaterialDto[];

  @ApiProperty({
    description: "Array of test results with individual min/max ranges per grade",
    example: [
      {
        condition: "at 23°C",
        grades: {
          a: { maxRange: 1300, minRange: 1100, value: 1200 },
        },
        propertyName: "tensileModulus",
        standard: "ISO 527-2",
        testName: "Tensile modulus (MPa)",
      },
      {
        condition: "230°C 2,16kg",
        grades: {
          a: { maxRange: 26, minRange: 22, value: 24 },
        },
        propertyName: "mfi",
        standard: "ISO 1133",
        testName: "MFI (g/10min)",
      },
      {
        condition: "at 23°C, V Notch",
        grades: {
          a: { maxRange: 27, minRange: 23, value: 25.2 },
        },
        propertyName: "nIzod",
        standard: "ISO 527",
        testName: "Notched Izod (kJ/m²)",
      },
      {
        grades: {
          a: { value: 25.1 },
        },
        propertyName: "ashContent",
        standard: "ISO 3451-1",
        testName: "Ash content (%)",
      },
      {
        grades: {
          a: { value: 1.025 },
        },
        propertyName: "density",
        standard: "ISO 1183",
        testName: "Density (g/cm³)",
      },
      {
        grades: {
          a: { value: 1200 },
        },
        propertyName: "price",
        testName: "Price (€/ton)",
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateFormulationTestResultDto)
  public testResults: CreateFormulationTestResultDto[];
}
