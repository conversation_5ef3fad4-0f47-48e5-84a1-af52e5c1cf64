import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNumber, IsOptional, IsString } from "class-validator";

export class MaterialCriteriaDto {
  @ApiProperty({
    description: "Material ID",
    example: "uuid1",
  })
  @IsString()
  public materialId: string;

  @ApiProperty({
    description: "Minimum percentage/value for this material",
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  public minValue?: number;

  @ApiProperty({
    description: "Maximum percentage/value for this material",
    example: 50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  public maxValue?: number;
}
