import { ApiProperty } from "@nestjs/swagger";
import { FormulationMaterialDto } from "./formulation-material.dto";
import { FormulationTestResultDto } from "./formulation-test-result.dto";

export class FormulationResponseDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "35894c97-7fff-424f-82c5-f0550cf2faf1",
  })
  public formulationId: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper",
  })
  public name: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  public grade: string;

  @ApiProperty({
    description: "Owner ID",
    example: "owner-user-id",
  })
  public ownerId: string;

  @ApiProperty({
    description: "Indicates if the current user has access to view this formulation's materials and test results",
    example: true,
  })
  public isAccessible: boolean;

  @ApiProperty({
    description: "Array of materials in this formulation",
    type: [FormulationMaterialDto],
  })
  public materials: FormulationMaterialDto[];

  @ApiProperty({
    description: "Array of test results for this formulation",
    type: [FormulationTestResultDto],
  })
  public testResults: FormulationTestResultDto[];
}
