import { ApiProperty } from "@nestjs/swagger";

export class MaterialInFormulationDto {
  @ApiProperty({
    description: "Material ID",
    example: "94f114ca-6504-488f-9083-444b59b6177a",
  })
  public materialId: string;

  @ApiProperty({
    description: "Material type name",
    example: "Virgin PP Homopolymer",
  })
  public materialType: string;

  @ApiProperty({
    description: "Material reference code",
    example: "PP-H 1200",
  })
  public reference: string;

  @ApiProperty({
    description: "Material percentage value in the formulation",
    example: 70,
  })
  public value: number;

  @ApiProperty({
    description: "Indicates if this material was included in the search criteria",
    example: true,
  })
  public searched: boolean;
}
