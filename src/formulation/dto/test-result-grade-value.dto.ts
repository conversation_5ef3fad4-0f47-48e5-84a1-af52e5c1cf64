import { ApiProperty } from "@nestjs/swagger";
import { IsNumber, IsOptional } from "class-validator";

export class TestResultGradeValueDto {
  @ApiProperty({
    description: "Test result value",
    example: 1200,
  })
  @IsNumber()
  public value: number;

  @ApiProperty({
    description: "Minimum acceptable range for this grade",
    example: 1100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  public minRange?: number;

  @ApiProperty({
    description: "Maximum acceptable range for this grade",
    example: 1300,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  public maxRange?: number;
}
