import { ApiProperty } from "@nestjs/swagger";
import { MaterialInFormulationDto } from "./material-in-formulation.dto";
import { TestResultDto } from "./test-result.dto";

export class FormulationComparisonDto {
  @ApiProperty({
    description: "Formulation ID",
    example: "35894c97-7fff-424f-82c5-f0550cf2faf1",
  })
  public formulationId: string;

  @ApiProperty({
    description: "Formulation name",
    example: "Bumper",
  })
  public name: string;

  @ApiProperty({
    description: "Formulation grade",
    example: "A",
  })
  public grade: string;

  @ApiProperty({
    description: "Indicates if the current user has access to view this formulation's materials and test results",
    example: true,
  })
  public isAccessible: boolean;

  @ApiProperty({
    description: "Array of materials in this formulation with details",
    type: [MaterialInFormulationDto],
  })
  public materials: MaterialInFormulationDto[];

  @ApiProperty({
    description: "Count of materials that match the search criteria",
    example: 1,
  })
  public matchingMaterialsCount: number;

  @ApiProperty({
    description: "Array of test results for this formulation",
    type: [TestResultDto],
  })
  public testResults: TestResultDto[];

  @ApiProperty({
    description: "Tier level based on optional criteria matching (1=highest, ascending numbers for lower tiers)",
    example: 1,
    required: false,
  })
  public tier?: number;

  @ApiProperty({
    description: "Count of optional criteria matched",
    example: 3,
  })
  public optionalCriteriaMatched: number;

  @ApiProperty({
    description: "Total count of optional criteria in the request",
    example: 5,
  })
  public totalOptionalCriteria: number;
}
