import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsN<PERSON>ber, IsOptional, IsPositive, ValidateNested } from "class-validator";
import { CriteriaDto } from "./criteria.dto";
import { MaterialCriteriaDto } from "./material-criteria.dto";

export class ComparisonRequestDto {
  @ApiProperty({
    description: "Array of material criteria with optional min/max values",
    example: [
      {
        materialId: "uuid1",
        maxValue: 50,
        minValue: 10,
      },
      {
        materialId: "uuid2",
        minValue: 5,
      },
    ],
    required: false,
    type: [MaterialCriteriaDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MaterialCriteriaDto)
  public materialCriteria?: MaterialCriteriaDto[];

  @ApiProperty({
    default: 1,
    description: "Page number for pagination",
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  public page?: number = 1;

  @ApiProperty({
    default: 10,
    description: "Number of items per page",
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @IsPositive()
  public limit?: number = 10;

  @ApiProperty({
    description: "Array of test criteria to filter formulations",
    example: [
      {
        operator: ">=",
        propertyName: "mfi",
        required: true,
        value: 5,
      },
      {
        operator: "<=",
        propertyName: "density",
        required: false,
        tier: 1,
        value: 1.2,
      },
    ],
    required: false,
    type: [CriteriaDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CriteriaDto)
  public criteria?: CriteriaDto[];
}
