import { ApiProperty } from "@nestjs/swagger";
import { PaginationMetaDto } from "../../common/dto";
import { FormulationComparisonDto } from "./formulation-comparison.dto";

export class ComparisonResponseDto {
  @ApiProperty({
    description: "List of formulation comparisons",
    type: [FormulationComparisonDto],
  })
  public data: FormulationComparisonDto[];

  @ApiProperty({
    description: "Pagination metadata",
    type: PaginationMetaDto,
  })
  public meta: PaginationMetaDto;
}
