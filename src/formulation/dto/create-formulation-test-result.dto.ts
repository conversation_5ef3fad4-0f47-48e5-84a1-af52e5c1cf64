import { ApiProperty } from "@nestjs/swagger";
import { IsObject, IsOptional, IsString } from "class-validator";
import { TestResultGradeValueDto } from "./test-result-grade-value.dto";

export class CreateFormulationTestResultDto {
  @ApiProperty({
    description: "Name of the test performed",
    example: "Tensile Strength",
  })
  @IsString()
  public testName: string;

  @ApiProperty({
    description: "Testing standard used",
    example: "ASTM D638",
    required: false,
  })
  @IsOptional()
  @IsString()
  public standard?: string;

  @ApiProperty({
    description: "Testing conditions",
    example: "23°C, 50% RH",
    required: false,
  })
  @IsOptional()
  @IsString()
  public condition?: string;

  @ApiProperty({
    description: "Standardized property name identifier for this test",
    example: "tensileModulus",
  })
  @IsString()
  public propertyName: string;

  @ApiProperty({
    description: "Test result values with individual min/max ranges per grade",
    example: {
      a: { maxRange: 1300, minRange: 1100, value: 1200 },
      b: { maxRange: 1200, minRange: 1000, value: 1100 },
      c: { maxRange: 1250, minRange: 1050, value: 1150 },
      d: { maxRange: 1180, minRange: 980, value: 1080 },
      e: { maxRange: 1350, minRange: 1150, value: 1250 },
    },
  })
  @IsObject()
  public grades: Record<string, TestResultGradeValueDto>;
}
