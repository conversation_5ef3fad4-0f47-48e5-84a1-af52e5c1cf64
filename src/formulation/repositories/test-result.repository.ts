import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";

@Injectable()
export class TestResultRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findByFormulationIds(formulationIds: string[]) {
    if (formulationIds.length === 0) {
      return [];
    }

    return await this.prisma.testResult.findMany({
      include: {
        formulation: {
          select: {
            grade: true,
            id: true,
          },
        },
      },
      where: {
        formulationId: {
          in: formulationIds,
        },
      },
    });
  }
}
