import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { TestResultRepository } from "./test-result.repository";

describe("TestResultRepository", () => {
  let repository: TestResultRepository;

  const mockPrisma = {
    testResult: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TestResultRepository,
        { provide: PrismaService, useValue: mockPrisma },
      ],
    }).compile();

    repository = module.get<TestResultRepository>(TestResultRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findByFormulationIds", () => {
    const mockTestResults = [
      {
        condition: "at 23°C",
        formulation: {
          grade: "A",
          id: "formulation-1",
        },
        formulationId: "formulation-1",
        id: "test-result-1",
        maxRange: 1380,
        minRange: 1100,
        standard: "ISO 527-2",
        testName: "Tensile modulus",
        value: 1200,
      },
      {
        condition: "230°C 2,16kg",
        formulation: {
          grade: "B",
          id: "formulation-2",
        },
        formulationId: "formulation-2",
        id: "test-result-2",
        maxRange: 20.1,
        minRange: 17.3,
        standard: "ISO 1133",
        testName: "MFI",
        value: 19,
      },
    ];

    it("should find test results by formulation IDs", async () => {
      const formulationIds = ["formulation-1", "formulation-2"];
      mockPrisma.testResult.findMany.mockResolvedValue(mockTestResults);

      const result = await repository.findByFormulationIds(formulationIds);

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith({
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      });

      expect(result).toEqual(mockTestResults);
    });

    it("should find test results with single formulation ID", async () => {
      const formulationIds = ["formulation-1"];
      const singleResult = [mockTestResults[0]];
      mockPrisma.testResult.findMany.mockResolvedValue(singleResult);

      const result = await repository.findByFormulationIds(formulationIds);

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith({
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      });

      expect(result).toEqual(singleResult);
    });

    it("should return empty array when no formulation IDs provided", async () => {
      const formulationIds: string[] = [];

      const result = await repository.findByFormulationIds(formulationIds);

      expect(mockPrisma.testResult.findMany).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it("should return empty array when no test results found", async () => {
      const formulationIds = ["non-existent-formulation"];
      mockPrisma.testResult.findMany.mockResolvedValue([]);

      const result = await repository.findByFormulationIds(formulationIds);

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith({
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      });

      expect(result).toEqual([]);
    });

    it("should handle duplicate formulation IDs", async () => {
      const formulationIds = ["formulation-1", "formulation-1", "formulation-2"];
      mockPrisma.testResult.findMany.mockResolvedValue(mockTestResults);

      const result = await repository.findByFormulationIds(formulationIds);

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith({
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      });

      expect(result).toEqual(mockTestResults);
    });

    it("should handle many formulation IDs", async () => {
      const formulationIds = Array.from({ length: 100 }, (_, index) => `formulation-${index}`);
      const manyResults = formulationIds.map((id, index) => ({
        condition: `condition-${index}`,
        formulation: {
          grade: `Grade-${index}`,
          id,
        },
        formulationId: id,
        id: `test-result-${index}`,
        maxRange: 100 + index,
        minRange: 50 + index,
        standard: `Standard-${index}`,
        testName: `Test-${index}`,
        value: 75 + index,
      }));
      mockPrisma.testResult.findMany.mockResolvedValue(manyResults);

      const result = await repository.findByFormulationIds(formulationIds);

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith({
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      });

      expect(result).toEqual(manyResults);
    });

    it("should handle database errors", async () => {
      const formulationIds = ["formulation-1"];
      const databaseError = new Error("Database connection failed");
      mockPrisma.testResult.findMany.mockRejectedValue(databaseError);

      await expect(repository.findByFormulationIds(formulationIds)).rejects.toThrow(
        "Database connection failed"
      );

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith({
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      });
    });

    it("should include correct formulation data in results", async () => {
      const formulationIds = ["formulation-1"];
      const resultWithFormulation = [
        {
          condition: "at 23°C",
          formulation: {
            grade: "Premium Grade A",
            id: "formulation-1",
          },
          formulationId: "formulation-1",
          id: "test-result-1",
          maxRange: 1500,
          minRange: 1000,
          standard: "ISO 527-2",
          testName: "Tensile modulus",
          value: 1250,
        },
      ];
      mockPrisma.testResult.findMany.mockResolvedValue(resultWithFormulation);

      const result = await repository.findByFormulationIds(formulationIds);

      expect(result).toEqual(resultWithFormulation);
      expect(result[0].formulation).toBeDefined();
      expect(result[0].formulation.grade).toBe("Premium Grade A");
      expect(result[0].formulation.id).toBe("formulation-1");
    });

    it("should verify correct query structure with includes", async () => {
      const formulationIds = ["test-id"];
      mockPrisma.testResult.findMany.mockResolvedValue([]);

      await repository.findByFormulationIds(formulationIds);

      const expectedQuery = {
        include: {
          formulation: {
            select: {
              grade: true,
              id: true,
            },
          },
        },
        where: {
          formulationId: {
            in: formulationIds,
          },
        },
      };

      expect(mockPrisma.testResult.findMany).toHaveBeenCalledWith(expectedQuery);
    });
  });
});
