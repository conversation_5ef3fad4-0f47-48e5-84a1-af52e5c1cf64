import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { UserRole } from "../../role/role.types";
import { FormulationRepository } from "./formulation.repository";
import { ACCESS_STATUS } from "@/formulation/enum/access-status.enum";
import { RequestStatus } from "@/generated/prisma";

describe("FormulationRepository", () => {
  let repository: FormulationRepository;

  const mockPrisma = {
    $transaction: jest.fn(),
    formulation: {
      count: jest.fn(),
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    material: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FormulationRepository,
        { provide: PrismaService, useValue: mockPrisma },
      ],
    }).compile();
    repository = module.get<FormulationRepository>(FormulationRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(repository).toBeDefined();
  });

  describe("getFormulationById", () => {
    it("should return formulation when found", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const mockFormulation = {
        grade: "A",
        id: formulationId,
        materials: [
          {
            material: {
              reference: "PP-H 1200",
              type: "Virgin PP Homopolymer",
            },
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            percentage: 70,
          },
        ],
        name: "Bumper",
        ownerId: "owner-user-id",
        requests: [],
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };
      mockPrisma.formulation.findUnique.mockResolvedValue(mockFormulation);
      const result = await repository.getFormulationById(formulationId);
      expect(mockPrisma.formulation.findUnique).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: formulationId },
        })
      );
      expect(result).toEqual(mockFormulation);
    });
    it("should return null when formulation not found", async () => {
      const formulationId = "non-existent-id";
      mockPrisma.formulation.findUnique.mockResolvedValue(undefined as never);
      const result = await repository.getFormulationById(formulationId);
      expect(mockPrisma.formulation.findUnique).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: formulationId },
        })
      );
      expect(result).toBeUndefined();
    });
  });

  it("should return paginated formulations with no filter", async () => {
    mockPrisma.formulation.findMany.mockResolvedValue([
      {
        grade: "A",
        id: "form-1",
        materials: [],
        name: "Bumper",
        ownerId: "owner-1",
        requests: [],
        testResults: [],
      },
    ]);
    const result = await repository.getFormulations({});
    expect(result.formulations).toHaveLength(1);
    expect(result.total).toBe(1);
  });

  it("should filter by name and grade", async () => {
    mockPrisma.formulation.findMany.mockResolvedValue([
      {
        grade: "B",
        id: "form-2",
        materials: [],
        name: "Dashboard",
        ownerId: "owner-2",
        requests: [],
        testResults: [],
      },
    ]);
    const result = await repository.getFormulations({ grade: "B", name: "Dash" });
    expect(result.formulations[0].name).toContain("Dashboard");
    expect(result.formulations[0].grade).toBe("B");
  });

  it("should handle search filter", async () => {
    mockPrisma.formulation.findMany.mockResolvedValue([
      {
        grade: "C",
        id: "form-3",
        materials: [],
        name: "TestForm",
        ownerId: "owner-3",
        requests: [],
        testResults: [],
      },
    ]);
    const result = await repository.getFormulations({ search: "TestForm" });
    expect(result.formulations[0].name).toBe("TestForm");
  });

  it("should map isAccessible for ENGINEERS with ownership", async () => {
    mockPrisma.formulation.findMany.mockResolvedValue([
      {
        grade: "A",
        id: "form-4",
        materials: [],
        name: "OwnedForm",
        ownerId: "engineer-1",
        requests: [],
        testResults: [],
      },
    ]);
    const result = await repository.getFormulations({ userId: "engineer-1", userRole: UserRole.ENGINEERS });
    expect(result.formulations[0].accessStatus).toBe(ACCESS_STATUS.ACCESSED);
    expect(result.formulations[0].isAccessible).toBe(true);
  });

  it("should map isAccessible for ENGINEERS with approved request", async () => {
    mockPrisma.formulation.findMany.mockResolvedValue([
      {
        grade: "A",
        id: "form-5",
        materials: [],
        name: "RequestForm",
        ownerId: "owner-5",
        requests: [
          { id: "req-1", requesterId: "engineer-2", status: RequestStatus.APPROVED },
        ],
        testResults: [],
      },
    ]);
    const result = await repository.getFormulations({ userId: "engineer-2", userRole: UserRole.ENGINEERS });
    expect(result.formulations[0].accessStatus).toBe(ACCESS_STATUS.ACCESSED);
    expect(result.formulations[0].isAccessible).toBe(true);
  });

  it("should map isAccessible false for ENGINEERS without ownership or approval", async () => {
    mockPrisma.formulation.findMany.mockResolvedValue([
      {
        grade: "A",
        id: "form-6",
        materials: [],
        name: "NoAccessForm",
        ownerId: "owner-6",
        requests: [
          { id: "req-2", requesterId: "engineer-3", status: RequestStatus.PENDING_APPROVAL },
        ],
        testResults: [],
      },
    ]);
    const result = await repository.getFormulations({ userId: "engineer-4", userRole: UserRole.ENGINEERS });
    expect(result.formulations[0].accessStatus).toBe(ACCESS_STATUS.REQUIRED);
    expect(result.formulations[0].isAccessible).toBe(false);
  });

  describe("createFormulations", () => {
    it("should create formulations successfully", async () => {
      const dto = {
        code: "TEST-001",
        materials: [
          { grades: { gradeA: 70 }, materialId: "mat-1" },
          { grades: { gradeA: 30 }, materialId: "mat-2" },
        ],
        name: "Bumper",
        testResults: [
          {
            condition: "23C",
            grades: { gradeA: { maxRange: 110, minRange: 90, value: 100 } },
            propertyName: "tensile",
            standard: "ASTM",
            testName: "Tensile",
          },
        ],
      };
      const ownerId = "user-1";
      mockPrisma.material.findMany.mockResolvedValue([{ id: "mat-1" }, { id: "mat-2" }]);
      mockPrisma.$transaction.mockImplementation(async (callback: (tx: unknown) => Promise<unknown>): Promise<unknown> => {
        return await callback({
          formulation: {
            create: jest.fn().mockResolvedValue({
              grade: "gradeA",
              id: "form-1",
              name: "Bumper",
              ownerId,
            }),
          },
        });
      });
      const result = await repository.createFormulations(dto as never, ownerId);
      expect(result).toBeDefined();
      expect(mockPrisma.material.findMany).toHaveBeenCalled();
      expect(mockPrisma.$transaction).toHaveBeenCalled();
    });

    it("should throw BadRequestException if material not found", async () => {
      const dto = {
        code: "TEST-002",
        materials: [
          { grades: { gradeA: 70 }, materialId: "mat-1" },
          { grades: { gradeA: 30 }, materialId: "mat-2" },
        ],
        name: "Bumper",
        testResults: [],
      };
      const ownerId = "user-1";
      mockPrisma.material.findMany.mockResolvedValue([{ id: "mat-1" }]);
      await expect(repository.createFormulations(dto as never, ownerId)).rejects.toThrow("Materials not found: mat-2");
    });

    it("should propagate error from prisma transaction", async () => {
      const dto = {
        code: "TEST-003",
        materials: [
          { grades: { gradeA: 70 }, materialId: "mat-1" },
        ],
        name: "Bumper",
        testResults: [],
      };
      const ownerId = "user-1";
      mockPrisma.material.findMany.mockResolvedValue([{ id: "mat-1" }]);
      mockPrisma.$transaction.mockRejectedValue(new Error("DB error"));
      await expect(repository.createFormulations(dto as never, ownerId)).rejects.toThrow("DB error");
    });
  });

  describe("buildWhereClause", () => {
    it("should build where clause with all filters", () => {
      const filter = { grade: "A", name: "Bumper", ownerId: "user-1" };
      const where = (repository as never as { buildWhereClause: (filter: Record<string, unknown>) => Record<string, unknown> }).buildWhereClause(filter);
      expect(where).toEqual({ grade: "A", name: { contains: "Bumper", mode: "insensitive" }, ownerId: "user-1" });
    });
    it("should build where clause with partial filters", () => {
      const filter = { name: "Bumper" };
      const where = (repository as never as { buildWhereClause: (filter: Record<string, unknown>) => Record<string, unknown> }).buildWhereClause(filter);
      expect(where).toEqual({ name: { contains: "Bumper", mode: "insensitive" } });
    });
    it("should build empty where clause if no filters", () => {
      const filter = {};
      const where = (repository as never as { buildWhereClause: (filter: Record<string, unknown>) => Record<string, unknown> }).buildWhereClause(filter);
      expect(where).toEqual({});
    });
  });

  describe("mapFormulation - ACCESS_STATUS Logic", () => {
    const baseFormulation = {
      grade: "A",
      id: "form-1",
      materials: [
        {
          material: { reference: "PP-H 1200", type: "Polymer" },
          materialId: "mat-1",
          percentage: 70,
        },
      ],
      name: "Test Formulation",
      ownerId: "owner-id",
      requests: [],
      testResults: [
        {
          condition: "23C",
          id: "test-1",
          maxRange: 110,
          minRange: 90,
          standard: "ASTM",
          testName: "Tensile",
          value: 100,
        },
      ],
    };

    describe("when user is owner", () => {
      it("should return ACCESSED status and full access", () => {
        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          baseFormulation,
          UserRole.ENGINEERS,
          "owner-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.ACCESSED);
        expect(result.isAccessible).toBe(true);
        expect(result.materials).toHaveLength(1);
        expect(result.testResults).toHaveLength(1);
      });
    });

    describe("when user is admin", () => {
      it("should return ACCESSED status and full access", () => {
        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          baseFormulation,
          UserRole.ADMIN,
          "other-user-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.ACCESSED);
        expect(result.isAccessible).toBe(true);
        expect(result.materials).toHaveLength(1);
        expect(result.testResults).toHaveLength(1);
      });
    });

    describe("when user is engineer (non-owner)", () => {
      it("should return REQUIRED status when no request exists", () => {
        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          baseFormulation,
          UserRole.ENGINEERS,
          "engineer-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.REQUIRED);
        expect(result.isAccessible).toBe(false);
        expect(result.materials).toEqual([]);
        expect(result.testResults).toEqual([]);
      });

      it("should return PENDING status when request is pending approval", () => {
        const formulationWithPendingRequest = {
          ...baseFormulation,
          requests: [
            {
              id: "req-1",
              requesterId: "engineer-id",
              status: RequestStatus.PENDING_APPROVAL,
            },
          ],
        };

        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          formulationWithPendingRequest,
          UserRole.ENGINEERS,
          "engineer-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.PENDING);
        expect(result.isAccessible).toBe(false);
        expect(result.materials).toEqual([]);
        expect(result.testResults).toEqual([]);
      });

      it("should return ACCESSED status when request is approved", () => {
        const formulationWithApprovedRequest = {
          ...baseFormulation,
          requests: [
            {
              id: "req-1",
              requesterId: "engineer-id",
              status: RequestStatus.APPROVED,
            },
          ],
        };

        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          formulationWithApprovedRequest,
          UserRole.ENGINEERS,
          "engineer-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.ACCESSED);
        expect(result.isAccessible).toBe(true);
        expect(result.materials).toHaveLength(1);
        expect(result.testResults).toHaveLength(1);
      });

      it("should return REJECTED status when request is rejected", () => {
        const formulationWithRejectedRequest = {
          ...baseFormulation,
          requests: [
            {
              id: "req-1",
              requesterId: "engineer-id",
              status: RequestStatus.REJECTED,
            },
          ],
        };

        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          formulationWithRejectedRequest,
          UserRole.ENGINEERS,
          "engineer-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.REJECTED);
        expect(result.isAccessible).toBe(false);
        expect(result.materials).toEqual([]);
        expect(result.testResults).toEqual([]);
      });

      it("should return REQUIRED status for unknown request status", () => {
        const formulationWithUnknownRequest = {
          ...baseFormulation,
          requests: [
            {
              id: "req-1",
              requesterId: "engineer-id",
              status: "UNKNOWN_STATUS",
            },
          ],
        };

        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          formulationWithUnknownRequest,
          UserRole.ENGINEERS,
          "engineer-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.REQUIRED);
        expect(result.isAccessible).toBe(false);
        expect(result.materials).toEqual([]);
        expect(result.testResults).toEqual([]);
      });

      it("should return REQUIRED status when other users have requests", () => {
        const formulationWithOtherRequests = {
          ...baseFormulation,
          requests: [
            {
              id: "req-1",
              requesterId: "different-engineer-id",
              status: RequestStatus.APPROVED,
            },
          ],
        };

        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          formulationWithOtherRequests,
          UserRole.ENGINEERS,
          "engineer-id"
        );

        expect(result.accessStatus).toBe(ACCESS_STATUS.REQUIRED);
        expect(result.isAccessible).toBe(false);
        expect(result.materials).toEqual([]);
        expect(result.testResults).toEqual([]);
      });
    });

    describe("return object structure", () => {
      it("should return correct object structure", () => {
        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          baseFormulation,
          UserRole.ENGINEERS,
          "owner-id"
        );

        expect(result).toEqual({
          accessStatus: ACCESS_STATUS.ACCESSED,
          formulationId: "form-1",
          grade: "A",
          isAccessible: true,
          materials: [
            {
              materialId: "mat-1",
              materialType: "Polymer",
              reference: "PP-H 1200",
              value: 70,
            },
          ],
          name: "Test Formulation",
          ownerId: "owner-id",
          testResults: [
            {
              condition: "23C",
              id: "test-1",
              maxRange: 110,
              minRange: 90,
              standard: "ASTM",
              testName: "Tensile",
              value: 100,
            },
          ],
        });
      });

      it("should handle null/undefined values in test results", () => {
        const formulationWithNulls = {
          ...baseFormulation,
          testResults: [
            {
              condition: undefined,
              id: "test-1",
              maxRange: undefined,
              minRange: undefined,
              standard: undefined,
              testName: "Tensile",
              value: 100,
            },
          ],
        };

        const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(
          formulationWithNulls,
          UserRole.ENGINEERS,
          "owner-id"
        );

        expect((result.testResults as unknown[])[0]).toEqual({
          condition: undefined,
          id: "test-1",
          maxRange: undefined,
          minRange: undefined,
          standard: undefined,
          testName: "Tensile",
          value: 100,
        });
      });
    });
  });

  describe("mapFormulation", () => {
    it("should map formulation with isAccessible true (non-engineer)", () => {
      const formulation = {
        grade: "A",
        id: "form-1",
        materials: [
          { material: { reference: "PP-H 1200", type: "Polymer" }, materialId: "mat-1", percentage: 70 },
        ],
        name: "Bumper",
        ownerId: "owner-1",
        requests: [],
        testResults: [
          { condition: "23C", id: "test-1", maxRange: 110, minRange: 90, standard: "ASTM", testName: "Tensile", value: 100 },
        ],
      };
      const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(formulation, "ADMIN", "admin-1");
      expect(result.isAccessible).toBe(true);
      expect(result.materials).toHaveLength(1);
      expect(result.testResults).toHaveLength(1);
    });
    it("should map formulation with isAccessible false (engineer, not owner, not approved)", () => {
      const formulation = {
        grade: "A",
        id: "form-2",
        materials: [],
        name: "Bumper",
        ownerId: "owner-2",
        requests: [{ id: "req-1", requesterId: "eng-2", status: "PENDING" }],
        testResults: [],
      };
      const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(formulation, "ENGINEERS", "eng-1");
      expect(result.isAccessible).toBe(false);
      expect(result.materials).toHaveLength(0);
      expect(result.testResults).toHaveLength(0);
    });
    it("should map formulation with isAccessible true (engineer, owner)", () => {
      const formulation = {
        grade: "A",
        id: "form-3",
        materials: [],
        name: "Bumper",
        ownerId: "eng-3",
        requests: [],
        testResults: [],
      };
      const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(formulation, "ENGINEERS", "eng-3");
      expect(result.isAccessible).toBe(true);
    });
    it("should map formulation with isAccessible true (engineer, approved request)", () => {
      const formulation = {
        grade: "A",
        id: "form-4",
        materials: [],
        name: "Bumper",
        ownerId: "owner-4",
        requests: [{ id: "req-2", requesterId: "eng-4", status: "APPROVED" }],
        testResults: [],
      };
      const result = (repository as never as { mapFormulation: (formulation: unknown, userRole: string, userId: string) => Record<string, unknown> }).mapFormulation(formulation, "ENGINEERS", "eng-4");
      expect(result.isAccessible).toBe(true);
    });
  });
});
