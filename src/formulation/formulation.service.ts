import { Injectable } from "@nestjs/common";
import { createPaginatedResponse, normalizePaginationParameters } from "../common/utils/pagination.utility";
import { CreateFormulationDto, CreateFormulationResponseDto, CreateSimulationDto, CreateSimulationResponseDto, ResponseSimulationTestResultDto } from "./dto";
import { FormulationFilterDto } from "./dto/formulation-filter.dto";
import { PaginatedFormulationResponseDto } from "./dto/paginated-formulation-response.dto";
import { ResponseSimulationMaterialDto } from "./dto/simulation/response-simulation-material.dto";
import { FormulationRepository } from "./repositories/formulation.repository";
import { MaterialRepository, MaterialService, MaterialWithFamilyData } from "@/material";

@Injectable()
export class FormulationService {
  public constructor(
    private readonly formulationRepository: FormulationRepository,
    private readonly materialRepository: MaterialRepository,
    private readonly materialService: MaterialService,
  ) {}

  public async simulateFormulation(createSimulationDto: CreateSimulationDto): Promise<CreateSimulationResponseDto> {
    const { materialIds } = this.extractIds(createSimulationDto);

    const materials = await this.materialRepository.findByIds(materialIds);
    const aiPayload = this.generateAIPayload(materials, createSimulationDto);
    const aiBackendData = this.getMockAiResults();

    return {
      materials: aiPayload,
      real: [],
      simulationResults: aiBackendData,
    };
  }

  private getMockAiResults(): ResponseSimulationTestResultDto[] {
    return [
      {
        condition: "at 23°C",
        grades: [
          { grade: "A", maxRange: 1380, minRange: 1100, value: 1200 },
          { grade: "B", maxRange: 1500, minRange: 1260, value: 1320 },
          { grade: "C", maxRange: 1630, minRange: 1420, value: 1580 },
        ],
        id: "test-001",
        standard: "ISO 527-2",
        testName: "Tensile modulus",
      },
      {
        condition: "230°C 2,16kg",
        grades: [
          { grade: "A", maxRange: 27.1, minRange: 22.5, value: 24 },
          { grade: "B", maxRange: 20.1, minRange: 17.3, value: 19 },
          { grade: "C", maxRange: 14.2, minRange: 11.8, value: 13.5 },
        ],
        id: "test-002",
        standard: "ISO 1133",
        testName: "MFI",
      },
    ];
  }

  private extractIds(createSimulationDto: CreateSimulationDto): { formulationIds: string[], materialIds: string[] } {
    const formulationIds: string[] = [];
    const materialIds: string[] = [];

    for (const material of createSimulationDto.materials) {
      materialIds.push(material.materialId);
      if (material.grades) {
        for (const grade of material.grades) {
          if (grade.formulationId) {
            formulationIds.push(grade.formulationId);
          }
        }
      }
    }

    return { formulationIds, materialIds };
  }

  private generateAIPayload(materials: MaterialWithFamilyData[], createSimulationDto: CreateSimulationDto): ResponseSimulationMaterialDto[] {
    const updatedMaterials: ResponseSimulationMaterialDto[] = [];

    for (const material of materials) {
      const materialWithFamily = this.materialService.mapToMaterialPageResponse(material);
      const originalMaterial = createSimulationDto.materials.find(m => m.materialId === materialWithFamily.id);
      const grades = originalMaterial?.grades ?? [];
      delete materialWithFamily.familyData?.id;

      const newMaterial: ResponseSimulationMaterialDto = {
        grades,
        materialId: materialWithFamily.id,
        materialType: materialWithFamily.type,
        reference: materialWithFamily.reference,
        ...materialWithFamily.familyData,
      } as ResponseSimulationMaterialDto;

      updatedMaterials.push(newMaterial);
    }

    return updatedMaterials;
  }

  public async createFormulations(
    createFormulationDto: CreateFormulationDto,
    ownerId: string,
  ): Promise<CreateFormulationResponseDto> {
    const createdFormulations = await this.formulationRepository.createFormulations(
      createFormulationDto,
      ownerId,
    );

    return {
      data: createdFormulations,
    };
  }

  public async getFormulations(
    filter: FormulationFilterDto,
    userRole?: string,
    userId?: string
  ): Promise<PaginatedFormulationResponseDto> {
    const { page, limit } = normalizePaginationParameters(filter.page, filter.limit);

    const rbacFilters = {
      ...filter,
      limit,
      page,
      userId,
      userRole,
    };

    const { formulations, total } = await this.formulationRepository.getFormulations(rbacFilters);

    return createPaginatedResponse({
      data: formulations,
      limit,
      page,
      total,
    });
  }

  public async getFormulationById(
    id: string,
    userRole?: string,
    userId?: string
  ) {
    const formulation = await this.formulationRepository.getFormulationById(id);

    if (!formulation) {
      return;
    }

    return this.formulationRepository.mapFormulation(formulation, userRole, userId);
  }
}
