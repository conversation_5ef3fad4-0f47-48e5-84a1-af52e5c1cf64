import { Test, TestingModule } from "@nestjs/testing";
import { ComparisonService, UserContext } from "./comparison.service";
import { ComparisonRequestDto } from "./dto";
import { ComparisonRepository } from "./repositories/comparison.repository";

describe("ComparisonService", () => {
  let service: ComparisonService;
  let repository: ComparisonRepository;

  const mockComparisonRepository = {
    getFormulations: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ComparisonService,
        {
          provide: ComparisonRepository,
          useValue: mockComparisonRepository,
        },
      ],
    }).compile();

    service = module.get<ComparisonService>(ComparisonService);
    repository = module.get<ComparisonRepository>(ComparisonRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getComparisons", () => {
    it("should return formulations with materialCriteria filter for ADMIN", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        materialCriteria: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            minValue: 10,
          },
        ],
        page: 1,
      };

      const userContext: UserContext = {
        userId: "admin-user-id",
        userRole: "ADMIN",
      };

      const repositoryResult = {
        formulations: [
          {
            formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
            grade: "A",
            isAccessible: true,
            matchingMaterialsCount: 1,
            materials: [
              {
                materialId: "94f114ca-6504-488f-9083-444b59b6177a",
                materialType: "Virgin PP Homopolymer",
                reference: "PP-H 1200",
                searched: true,
                value: 70,
              },
            ],
            name: "Bumper",
            optionalCriteriaMatched: 0,
            testResults: [],
            tier: 1,
            totalOptionalCriteria: 0,
          },
        ],
        total: 1,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      const result = await service.getComparisons(request, userContext);

      expect(repository.getFormulations).toHaveBeenCalledWith(
        1,
        10,
        "admin-user-id",
        undefined,
        [{ materialId: "94f114ca-6504-488f-9083-444b59b6177a", minValue: 10 }],
        "ADMIN",
      );
      expect(result.data).toEqual(repositoryResult.formulations);
      expect(result.meta).toEqual({
        currentPage: 1,
        from: 1,
        lastPage: 1,
        perPage: 10,
        to: 1,
        total: 1,
      });
    });

    it("should return formulations with ownership filtering for ENGINEERS", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        materialCriteria: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            maxValue: 80,
          },
        ],
        page: 1,
      };

      const userContext: UserContext = {
        userId: "engineer-user-id",
        userRole: "ENGINEERS",
      };

      const repositoryResult = {
        formulations: [
          {
            formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
            grade: "A",
            isAccessible: false,
            matchingMaterialsCount: 1,
            materials: [
              {
                materialId: "94f114ca-6504-488f-9083-444b59b6177a",
                materialType: "Virgin PP Homopolymer",
                reference: "PP-H 1200",
                searched: true,
                value: 70,
              },
            ],
            name: "Bumper",
            optionalCriteriaMatched: 0,
            testResults: [],
            tier: 1,
            totalOptionalCriteria: 0,
          },
        ],
        total: 1,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      const result = await service.getComparisons(request, userContext);

      expect(repository.getFormulations).toHaveBeenCalledWith(
        1,
        10,
        "engineer-user-id",
        undefined,
        [{ materialId: "94f114ca-6504-488f-9083-444b59b6177a", maxValue: 80 }],
        "ENGINEERS",
      );
      expect(result.data).toEqual(repositoryResult.formulations);
    });

    it("should return formulations without materialCriteria filter", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        page: 1,
      };

      const repositoryResult = {
        formulations: [
          {
            formulationId: "formulation-1",
            grade: "B",
            isAccessible: true,
            matchingMaterialsCount: 0,
            materials: [
              {
                materialId: "material-1",
                materialType: "PP Base",
                reference: "PP-001",
                searched: false,
                value: 60,
              },
            ],
            name: "Standard Compound",
            optionalCriteriaMatched: 0,
            testResults: [],
            tier: 1,
            totalOptionalCriteria: 0,
          },
        ],
        total: 5,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      const result = await service.getComparisons(request);

      expect(repository.getFormulations).toHaveBeenCalledWith(
        1,
        10,
        undefined,
        undefined,
        undefined,
        undefined,
      );
      expect(result.data).toEqual(repositoryResult.formulations);
      expect(result.meta).toEqual({
        currentPage: 1,
        from: 1,
        lastPage: 1,
        perPage: 10,
        to: 5,
        total: 5,
      });
    });

    it("should use default pagination values", async () => {
      const request: ComparisonRequestDto = {};

      const repositoryResult = {
        formulations: [],
        total: 0,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      await service.getComparisons(request);

      expect(repository.getFormulations).toHaveBeenCalledWith(
        1,
        10,
        undefined,
        undefined,
        undefined,
        undefined,
      );
    });

    it("should calculate pagination metadata correctly", async () => {
      const request: ComparisonRequestDto = {
        limit: 5,
        page: 2,
      };

      const repositoryResult = {
        formulations: [],
        total: 12,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      const result = await service.getComparisons(request);

      expect(result.meta).toEqual({
        currentPage: 2,
        from: 6,
        lastPage: 3,
        perPage: 5,
        to: 10,
        total: 12,
      });
    });

    it("should handle empty results", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        materialCriteria: [
          {
            materialId: "non-existent-material",
          },
        ],
        page: 1,
      };

      const repositoryResult = {
        formulations: [],
        total: 0,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      const result = await service.getComparisons(request);

      expect(result.data).toEqual([]);
      expect(result.meta).toEqual({
        currentPage: 1,
        from: 0,
        lastPage: 0,
        perPage: 10,
        to: 0,
        total: 0,
      });
    });

    it("should calculate from and to correctly when total is zero", async () => {
      const request: ComparisonRequestDto = {
        limit: 10,
        page: 1,
      };

      const repositoryResult = {
        formulations: [],
        total: 0,
      };

      mockComparisonRepository.getFormulations.mockResolvedValue(repositoryResult);

      const result = await service.getComparisons(request);

      expect(result.meta.from).toBe(0);
      expect(result.meta.to).toBe(0);
    });
  });
});
