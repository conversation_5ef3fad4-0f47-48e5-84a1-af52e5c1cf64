import { Test, TestingModule } from "@nestjs/testing";
import { MaterialRepository, MaterialService } from "../material";
import { UserRole } from "../role/role.types";
import { FormulationService } from "./formulation.service";
import { FormulationRepository } from "./repositories/formulation.repository";

const mockFormulationRepository = {
  createFormulations: jest.fn(),
  getFormulationById: jest.fn(),
  getFormulations: jest.fn(),
  mapFormulation: jest.fn(),
};

const mockMaterialRepository = {
  findByIds: jest.fn(),
};

const mockMaterialService = {
  mapToMaterialPageResponse: jest.fn(),
};

describe("FormulationService", () => {
  let service: FormulationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FormulationService,
        {
          provide: FormulationRepository,
          useValue: mockFormulationRepository,
        },
        {
          provide: MaterialRepository,
          useValue: mockMaterialRepository,
        },
        {
          provide: MaterialService,
          useValue: mockMaterialService,
        },
      ],
    }).compile();

    service = module.get<FormulationService>(FormulationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("getFormulationById", () => {
    it("should return formulation when found", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = "ADMIN";
      const userId = "admin-user-id";

      const mockFormulation = {
        grade: "A",
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        materials: [
          {
            material: {
              reference: "PP-H 1200",
              type: "Virgin PP Homopolymer",
            },
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            percentage: 70,
          },
        ],
        name: "Bumper",
        ownerId: "owner-user-id",
        requests: [],
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        name: "Bumper",
        ownerId: "owner-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(mockFormulationRepository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(mockFormulationRepository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });

    it("should return null when formulation not found", async () => {
      const formulationId = "non-existent-id";
      const userRole = "ADMIN";
      const userId = "admin-user-id";

      const testValue = undefined as unknown;
      mockFormulationRepository.getFormulationById.mockResolvedValue(testValue);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(mockFormulationRepository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(mockFormulationRepository.mapFormulation).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });

    it("should handle ENGINEERS role with ownership", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = UserRole.ENGINEERS;
      const userId = "engineer-user-id";

      const mockFormulation = {
        grade: "A",
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        materials: [],
        name: "Bumper",
        ownerId: "engineer-user-id",
        requests: [],
        testResults: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        name: "Bumper",
        ownerId: "engineer-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(mockFormulationRepository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(mockFormulationRepository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });

    it("should handle ENGINEERS role without access", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = UserRole.ENGINEERS;
      const userId = "engineer-user-id";

      const mockFormulation = {
        grade: "A",
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        materials: [],
        name: "Bumper",
        ownerId: "other-user-id",
        requests: [],
        testResults: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: false,
        materials: [], // Empty because no access
        name: "Bumper",
        ownerId: "other-user-id",
        testResults: [], // Empty because no access
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(mockFormulationRepository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(mockFormulationRepository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });

    it("should handle undefined user role and userId", async () => {
      const formulationId = "35894c97-7fff-424f-82c5-f0550cf2faf1";
      const userRole = undefined;
      const userId = undefined;

      const mockFormulation = {
        grade: "A",
        id: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        materials: [],
        name: "Bumper",
        ownerId: "owner-user-id",
        requests: [],
        testResults: [],
      };

      const expectedMappedResult = {
        formulationId: "35894c97-7fff-424f-82c5-f0550cf2faf1",
        grade: "A",
        isAccessible: true,
        materials: [
          {
            materialId: "94f114ca-6504-488f-9083-444b59b6177a",
            materialType: "Virgin PP Homopolymer",
            reference: "PP-H 1200",
            value: 70,
          },
        ],
        name: "Bumper",
        ownerId: "owner-user-id",
        testResults: [
          {
            condition: "23°C, 50% RH",
            id: "test-result-1",
            maxRange: 30,
            minRange: 20,
            standard: "ASTM D638",
            testName: "Tensile Strength",
            value: 25.5,
          },
        ],
      };

      mockFormulationRepository.getFormulationById.mockResolvedValue(mockFormulation);
      mockFormulationRepository.mapFormulation.mockReturnValue(expectedMappedResult);

      const result = await service.getFormulationById(formulationId, userRole, userId);

      expect(mockFormulationRepository.getFormulationById).toHaveBeenCalledWith(formulationId);
      expect(mockFormulationRepository.mapFormulation).toHaveBeenCalledWith(mockFormulation, userRole, userId);
      expect(result).toEqual(expectedMappedResult);
    });
  });

  describe("createFormulations", () => {
    it("should create formulations and return response", async () => {
      const dto = { materials: [], name: "Bumper", testResults: [] };
      const ownerId = "user-1";
      const created = [{ grade: "A", id: "form-1", name: "Bumper", ownerId: "user-1" }];
      mockFormulationRepository.createFormulations.mockResolvedValue(created);
      const result = await service.createFormulations(dto as never, ownerId);
      expect(mockFormulationRepository.createFormulations).toHaveBeenCalledWith(dto, ownerId);
      expect(result).toEqual({ data: created });
    });
  });

  describe("getFormulations", () => {
    it("should return paginated formulations", async () => {
      const filter = { limit: 10, page: 1, search: "Bumper" };
      const userRole = "ADMIN";
      const userId = "user-1";
      const repoResult = {
        formulations: [
          {
            formulationId: "form-1",
            grade: "A",
            isAccessible: true,
            materials: [],
            name: "Bumper",
            ownerId: "user-1",
            testResults: [],
          },
        ],
        total: 1,
      };
      mockFormulationRepository.getFormulations.mockResolvedValue(repoResult);
      const result = await service.getFormulations(filter as never, userRole, userId);
      expect(mockFormulationRepository.getFormulations).toHaveBeenCalledWith({ ...filter, limit: 10, page: 1, userId, userRole });
      expect(result.data).toHaveLength(1);
      expect(result.meta.total).toBe(1);
    });

    it("should return empty paginated result", async () => {
      const filter = { limit: 10, page: 1 };
      mockFormulationRepository.getFormulations.mockResolvedValue({ formulations: [], total: 0 });
      const result = await service.getFormulations(filter as never);
      expect(result.data).toHaveLength(0);
      expect(result.meta.total).toBe(0);
    });
  });

  describe("simulateFormulation", () => {
    it("should call external API and return simulation results", async () => {
      const createSimulationDto = {
        materials: [
          {
            cma23EnergyPunctureAv: 1.2,
            cmaMinus20EnergyPunctureAv: 1,
            densityAv: 0.9,
            grades: [
              {
                code: "BMP-001",
                formulationId: "formulation-1",
                grade: "A",
                value: 70,
              },
            ],
            materialId: "material-1",
            materialType: "Virgin PP Homopolymer",
            mfiAv: 22.5,
            notchedIzodAv: 25,
            reference: "PP-H 1200",
            tensileModulusAv: 1300,
            value: 70,
          },
        ],
      };

      mockMaterialRepository.findByIds.mockResolvedValue([
        {
          familyData: {
            cma23EnergyPunctureAv: 1.2,
            cmaMinus20EnergyPunctureAv: 1,
            densityAv: 0.9,
            mfiAv: 22.5,
            notchedIzodAv: 25,
            tensileModulusAv: 1300,
          },
          id: "material-1",
          reference: "PP-H 1200",
          type: "Virgin PP Homopolymer",
        },
      ]);
      mockMaterialService.mapToMaterialPageResponse.mockReturnValue({
        familyData: {
          cma23EnergyPunctureAv: 1.2,
          cmaMinus20EnergyPunctureAv: 1,
          densityAv: 0.9,
          mfiAv: 22.5,
          notchedIzodAv: 25,
          tensileModulusAv: 1300,
        },
        id: "material-1",
        reference: "PP-H 1200",
        type: "Virgin PP Homopolymer",
      });

      const result = await service.simulateFormulation(createSimulationDto);

      expect(result).toBeDefined();
      expect(result.materials).toHaveLength(1);
      expect(result.materials[0]).toEqual({
        cma23EnergyPunctureAv: 1.2,
        cmaMinus20EnergyPunctureAv: 1,
        densityAv: 0.9,
        grades: [
          {
            code: "BMP-001",
            formulationId: "formulation-1",
            grade: "A",
            value: 70,
          },
        ],
        materialId: "material-1",
        materialType: "Virgin PP Homopolymer",
        mfiAv: 22.5,
        notchedIzodAv: 25,
        reference: "PP-H 1200",
        tensileModulusAv: 1300,
      });
      expect(result.simulationResults).toBeDefined();
      expect(result.real).toBeDefined();
    });
  });
});
