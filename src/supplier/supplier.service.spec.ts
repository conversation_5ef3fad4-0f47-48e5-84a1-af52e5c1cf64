import { NotFoundException } from "@nestjs/common";
import { SupplierDto } from "./dto/supplier.dto";
import { SupplierRepository } from "./repositories/supplier.repository";
import { SupplierService } from "./supplier.service";

describe("SupplierService", () => {
  let service: SupplierService;
  let mockSupplierRepository: { findOneByName: jest.Mock };

  beforeEach(() => {
    mockSupplierRepository = {
      findOneByName: jest.fn(),
    };
    service = new SupplierService(mockSupplierRepository as unknown as SupplierRepository);
  });

  it("should return SupplierDto if found", async () => {
    const supplier = { id: "1", name: "Test Supplier" };
    mockSupplierRepository.findOneByName.mockResolvedValue(supplier);
    const result = await service.findOneByName("Test Supplier");
    expect(mockSupplierRepository.findOneByName).toHaveBeenCalledWith("Test Supplier");
    expect(result).toEqual({ id: "1", name: "Test Supplier" } as SupplierDto);
  });

  it("should throw NotFoundException if not found", async () => {
    mockSupplierRepository.findOneByName.mockImplementation(() => Promise.resolve());
    await expect(service.findOneByName("Unknown")).rejects.toThrow(NotFoundException);
  });
});
