import { Test, TestingModule } from "@nestjs/testing";
import { PrismaService } from "../../prisma.service";
import { SupplierRepository } from "./supplier.repository";

describe("SupplierRepository", () => {
  let repository: SupplierRepository;
  let prisma: { supplier: { findUnique: jest.Mock } };

  beforeEach(async () => {
    prisma = {
      supplier: {
        findUnique: jest.fn(),
      },
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierRepository,
        { provide: PrismaService, useValue: prisma },
      ],
    }).compile();
    repository = module.get(SupplierRepository);
  });

  it("should call prisma.supplier.findUnique with correct name", async () => {
    const mockSupplier = { id: "1", name: "Test Supplier" };
    prisma.supplier.findUnique.mockResolvedValue(mockSupplier);
    const result = await repository.findOneByName("Test Supplier");
    expect(prisma.supplier.findUnique).toHaveBeenCalledWith({ where: { name: "Test Supplier" } });
    expect(result).toBe(mockSupplier);
  });

  it("should return undefined if supplier not found", async () => {
    prisma.supplier.findUnique.mockImplementation(() => Promise.resolve());
    const result = await repository.findOneByName("Unknown");
    expect(result).toBeUndefined();
  });
});
