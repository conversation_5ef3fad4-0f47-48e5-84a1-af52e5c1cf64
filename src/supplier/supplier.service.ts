import { Injectable, NotFoundException } from "@nestjs/common";
import { SupplierDto } from "./dto/supplier.dto";
import { SupplierRepository } from "./repositories/supplier.repository";
import { Supplier } from "@/generated/prisma";

@Injectable()
export class SupplierService {
  public constructor(private readonly supplierRepository: SupplierRepository) {}

  public async findOneByName(name: string): Promise<SupplierDto> {
    const result = await this.supplierRepository.findOneByName(name);

    if (!result) {
      throw new NotFoundException(`Supplier with name '${name}' not found`);
    }
    return this.mapToResponseDto(result);
  }

  private mapToResponseDto(supplier: Supplier): SupplierDto {
    return {
      id: supplier.id,
      name: supplier.name,
    };
  }
}
