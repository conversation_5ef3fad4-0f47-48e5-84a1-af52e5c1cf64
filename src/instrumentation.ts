import { AzureMonitorTraceExporter, AzureMonitorLogExporter } from "@azure/monitor-opentelemetry-exporter";
import { getNodeAutoInstrumentations } from "@opentelemetry/auto-instrumentations-node";
import { BatchLogRecordProcessor } from "@opentelemetry/sdk-logs";
import { NodeSDK } from "@opentelemetry/sdk-node";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-node";
import dotenv from "dotenv";

dotenv.config();

const connectionString = process.env.APPLICATIONINSIGHTS_CONNECTION_STRING;
if (connectionString) {
  const traceExporter = new AzureMonitorTraceExporter({
    connectionString,
  });
  const logExporter = new AzureMonitorLogExporter({
    connectionString,
  });

  const sdk = new NodeSDK({
    autoDetectResources: true,
    instrumentations: [
      getNodeAutoInstrumentations({
        ["@opentelemetry/instrumentation-nestjs-core"]: { enabled: true },
        ["@opentelemetry/instrumentation-winston"]: { disableLogSending: true, enabled: true },
      }),
    ],
    logRecordProcessors: [new BatchLogRecordProcessor(logExporter)],
    spanProcessors: [new BatchSpanProcessor(traceExporter)],
    traceExporter: traceExporter,
  });

  sdk.start();

  process.on("SIGTERM", () => {
    sdk.shutdown()
      .finally(() => process.exit(0))
      .catch(() => process.exit(1));
  });
}
