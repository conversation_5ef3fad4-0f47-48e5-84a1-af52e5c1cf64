import { Controller, Get, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOAuth2 } from "@nestjs/swagger";
import { AppService } from "./app.service";
import { AuthGuard } from "./auth/auth.guard";

@ApiBearerAuth()
@ApiOAuth2([process.env.AZURE_API_SCOPE ?? ""])
@UseGuards(AuthGuard)
@Controller()
export class AppController {
  public constructor(private readonly appService: AppService) {}

  @Get()
  public getHello(): string {
    return this.appService.getHello();
  }
}
