import { Injectable, CanActivate, ExecutionContext, SetMetadata, UnauthorizedException } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { AuthService } from "./auth.service";

export const ROLES = (...roles: string[]) => SetMetadata("roles", roles);

@Injectable()
export class RoleGuard implements CanActivate {
  public constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  public async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredRoles = this.reflector.get<string[]>("roles", context.getHandler());

    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request: { headers: { authorization?: string } } = context.switchToHttp().getRequest();
    const token = request.headers.authorization?.split(" ")[1] ?? "";

    if (!token) {
      throw new UnauthorizedException("No token provided");
    }

    try {
      const payload: Record<string, unknown> = await this.authService.validateToken(token);

      if (typeof payload === "object" && "oid" in payload) {
        const userId = payload.oid ?? "";
        const userRole = payload.role ?? "";
        if (!userId || !userRole) {
          return false;
        }

        const roleString = typeof userRole === "string" ? userRole : "";
        return requiredRoles.includes(roleString.toUpperCase());
      }

      return false;
    }
    catch {
      throw new UnauthorizedException("Invalid token");
    }
  }
}
