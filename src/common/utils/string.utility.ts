/**
 * Converts text to proper case (capitalizes first letter of each word)
 * @param text - The text to convert to proper case
 * @returns Text with first letter of each word capitalized
 */
export function capitalizeWords(text: string): string {
  if (!text || typeof text !== "string") {
    return "";
  }

  return text
    .toLowerCase()
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
