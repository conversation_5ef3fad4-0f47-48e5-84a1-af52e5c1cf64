import { ApiProperty } from "@nestjs/swagger";

export class PaginationMetaDto {
  @ApiProperty({ description: "Total number of items" })
  public total: number;

  @ApiProperty({ description: "Number of items per page" })
  public perPage: number;

  @ApiProperty({ description: "Current page number" })
  public currentPage: number;

  @ApiProperty({ description: "Last page number" })
  public lastPage: number;

  @ApiProperty({ description: "Index of first item on current page" })
  public from: number;

  @ApiProperty({ description: "Index of last item on current page" })
  public to: number;
}
