import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional, IsPositive } from "class-validator";

export class PaginationQueryDto {
  @ApiProperty({
    default: 1,
    description: "Page number",
    example: 1,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  public page?: number = 1;

  @ApiProperty({
    default: 10,
    description: "Number of items per page",
    example: 10,
    maximum: 100,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsPositive()
  public limit?: number = 10;
}
