import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { PaginationMetaDto } from "./pagination-meta.dto";

export class PaginatedResponseDto<T> {
  @ApiProperty({ description: "The data items for this page" })
  public data: T[];

  @ApiProperty({ description: "Pagination metadata", type: PaginationMetaDto })
  @Type(() => PaginationMetaDto)
  public meta: PaginationMetaDto;

  public constructor(data: T[], meta: PaginationMetaDto) {
    this.data = data;
    this.meta = meta;
  }
}
