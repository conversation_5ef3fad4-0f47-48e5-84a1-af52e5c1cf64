import { Module } from "@nestjs/common";
import { PrismaService } from "../prisma.service";
import { LocationService } from "./location.service";
import { LocationRepository } from "./repositories/location.repository";
import { AuthService } from "@/auth/auth.service";

@Module({
  exports: [LocationService, LocationRepository],
  providers: [LocationService, LocationRepository, AuthService, PrismaService],
})
export class LocationModule {}
