import { Injectable } from "@nestjs/common";
import { PrismaService } from "../../prisma.service";

@Injectable()
export class LocationRepository {
  public constructor(private readonly prisma: PrismaService) {}

  public async findAll() {
    return this.prisma.location.findMany({
      orderBy: [{ city: "asc" }, { country: "asc" }],
    });
  }

  public async findOne(id: string) {
    return this.prisma.location.findUnique({
      where: { id },
    });
  }
}
