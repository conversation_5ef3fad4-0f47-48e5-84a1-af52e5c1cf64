# Codebase Structure

## Main Directory Layout
```
/
├── src/                 # Main source code
├── prisma/             # Database schema and migrations
├── test/               # E2E tests
├── generated/          # Generated code (ignored by linter)
├── eslint-rules/       # Custom ESLint rules
└── package.json        # Dependencies and scripts
```

## Source Code Organization (src/)
Each feature is organized as a self-contained NestJS module:

### Core Files
- `main.ts` - Application entry point and bootstrap
- `app.module.ts` - Root application module
- `app.controller.ts` - Basic health check endpoints
- `app.service.ts` - Basic application services
- `prisma.service.ts` - Prisma database client service
- `instrumentation.ts` - OpenTelemetry setup
- `metadata.ts` - API metadata configuration

### Feature Modules
- `auth/` - Authentication and authorization (Azure AD)
- `user/` - User management and profiles
- `role/` - Role-based access control
- `department/` - Department management
- `location/` - Location/facility management
- `material/` - Material catalog and search
- `formulation/` - Recipe/formulation management
- `request/` - Access request and approval workflows
- `notification/` - Event-driven notification system
- `specification/` - Material specifications
- `email/` - Email communication services
- `supplier/` - Supplier information

### Shared Components
- `common/` - Shared DTOs, utilities, decorators, guards

## Module Architecture Pattern
Each module typically contains:
- `*.controller.ts` - HTTP endpoints and request handling
- `*.service.ts` - Business logic and orchestration
- `repositories/` - Data access layer with Prisma
- `dto/` - Request/response data transfer objects
- `events/` - Domain events for inter-module communication
- `*.spec.ts` - Unit tests (co-located with source)