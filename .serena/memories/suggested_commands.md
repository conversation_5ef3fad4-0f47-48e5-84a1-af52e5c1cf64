# Suggested Development Commands

## Essential Commands

### Development Workflow
```bash
# Install dependencies
npm install

# Start development server with auto-reload
npm run start:dev

# Start with debugging
npm run start:debug

# Build for production
npm run build

# Start production build
npm run start:prod
```

### Database Operations
```bash
# Generate Prisma client (run after schema changes)
npx prisma generate

# Push schema changes to database
npx prisma db push

# Run database migrations
npx prisma migrate dev

# Seed database
npx prisma db seed

# Access Prisma studio (database GUI)
npx prisma studio
```

### Testing
```bash
# Run all unit tests
npm run test

# Run tests with coverage
npm run test:cov

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e

# Run specific test file
npm test -- path/to/file.spec.ts

# Debug tests
npm run test:debug
```

### Code Quality
```bash
# Run linting
npm run lint

# Fix linting and format issues
npm run lint:fix

# Format code with Prettier
npm run format
```

### Git Operations (Darwin/macOS)
```bash
# Standard git commands
git status
git add .
git commit -m "message"
git push
git pull

# View git log
git log --oneline

# Check differences
git diff
```

### System Utilities (Darwin/macOS)
```bash
# List files and directories
ls -la

# Find files
find . -name "*.ts" -type f

# Search in files
grep -r "searchterm" src/

# Navigate directories
cd path/to/directory
pwd

# View file contents
cat filename
head -n 20 filename
tail -n 20 filename
```

## Development Server Access
- **API**: http://localhost:3000
- **Swagger Docs**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health