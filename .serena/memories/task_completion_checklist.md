# Task Completion Checklist

## Required Steps After Code Changes

### 1. Code Quality Checks
```bash
# Fix linting and formatting issues
npm run lint:fix

# Verify no remaining lint issues
npm run lint
```

### 2. Testing
```bash
# Run unit tests
npm run test

# Run tests with coverage (for significant changes)
npm run test:cov

# Run E2E tests (for API changes)
npm run test:e2e
```

### 3. Database Changes (if applicable)
```bash
# Generate Prisma client after schema changes
npx prisma generate

# Push schema changes to database
npx prisma db push
```

### 4. Build Verification
```bash
# Ensure project builds successfully
npm run build
```

### 5. Documentation Updates (if needed)
- Update API documentation (Swagger decorators)
- Update README.md for new features
- Update CLAUDE.md for development guidance changes

## Pre-Commit Requirements

The project uses Husky for Git hooks, which automatically runs:
- Lint checks
- Prisma formatting
- Commit message validation (conventional commits)

### Manual Verification Steps
1. **Type Safety**: All TypeScript errors resolved
2. **Import Organization**: Imports properly organized and using path aliases
3. **Code Style**: Following established conventions
4. **Test Coverage**: New functionality has corresponding tests
5. **Error Handling**: Proper error handling and validation
6. **Security**: No exposed secrets or security vulnerabilities

## Common Issues to Watch For
- **Accessibility Modifiers**: All class members have explicit public/private
- **Object Key Sorting**: Object properties in alphabetical order
- **Import Sorting**: Imports in alphabetical order
- **Naming Conventions**: Proper camelCase/PascalCase usage
- **No Comments in Functions**: Custom rule violation
- **Type Safety**: Avoiding `any` types and unsafe operations

## Environment Verification
- `.env` file properly configured
- Database connection working
- Azure AD authentication configured (or BYPASS_AUTH=true for development)