# Materiact Backend Project Overview

## Purpose
RESTful API for Materiact - A material management and formulation system for engineering teams. Handles material tracking, formulation creation, user management, and access requests with approval workflows.

## Tech Stack
- **Framework**: NestJS with Fastify adapter
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Azure AD OAuth2 with JWT tokens
- **Documentation**: Swagger/OpenAPI (available at /api-docs)
- **Testing**: Jest for unit and E2E tests
- **Monitoring**: Azure Monitor + OpenTelemetry + Winston logging
- **Email**: Azure Communication Services
- **Excel Export**: ExcelJS
- **Language**: TypeScript

## Prerequisites
- Node.js 22+
- npm/yarn
- PostgreSQL 14+

## Key Features
- Material management with polymorphic family relationships (POLYMERS, FILLERS, ELASTOMERS, ADDITIVES, RECYCLE_POLYMERS)
- Formulation creation and comparison system
- User management with role-based access control
- Request/approval workflow system
- Notification system with event-driven architecture
- Department and location management
- Excel export functionality
- Comprehensive test coverage