# Code Style and Conventions

## ESLint Configuration
The project uses extensive ESLint rules with TypeScript support and custom rules.

### Key Style Rules
- **Quotes**: Double quotes (`"`) required
- **Semicolons**: Required at end of statements
- **Naming Conventions**:
  - Variables/functions: camelCase
  - Classes/interfaces/types: PascalCase
  - Constants: UPPER_CASE
  - Enums: UPPER_CASE
  - Type parameters: PascalCase with "T" prefix
  - Private members: camelCase with optional leading underscore
- **Import Order**: Alphabetical ascending order required
- **Object Keys**: Sorted in ascending order (natural, case-sensitive)

### Custom Rules
- **No Comments in Functions**: Custom ESLint rule prevents comments inside function bodies
- **Class Member Ordering**: Specific order required:
  1. Static properties
  2. Static methods  
  3. Instance properties
  4. Private properties
  5. Constructor
  6. Methods
  7. Private methods

### Path Aliases
- Use `@/` prefix for imports (e.g., `@/common`, `@/auth`)
- Mapped in both tsconfig and Jest configuration

## TypeScript Conventions
- **Explicit Member Accessibility**: All class members must have explicit public/private modifiers
- **Type Safety**: No `any` types allowed, strict type checking enabled
- **Interface Naming**: No "I" prefix required
- **Type Assertions**: Consistent type assertion style required

## Architecture Patterns
- **Repository Pattern**: Each module uses separate service/repository layers
- **DTO Pattern**: Separate DTOs for requests/responses with validation
- **Event-Driven**: Domain events for inter-module communication
- **Guard System**: AuthGuard for authentication, RoleGuard for authorization

## File Organization
- **Co-located Tests**: Unit tests (`.spec.ts`) next to source files
- **Index Files**: Export definitions in `dto/index.ts` for clean imports
- **Naming**: Descriptive names following domain terminology

## Code Quality Requirements
- **Maximum Complexity**: 30 cyclomatic complexity
- **Maximum File Length**: 1000 lines (warning)
- **One Class per File**: Required
- **Default Case**: Required in switch statements
- **Guard Clauses**: Required for for-in loops