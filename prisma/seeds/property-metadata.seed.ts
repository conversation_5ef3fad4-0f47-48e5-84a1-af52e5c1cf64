import { MaterialFamily, PrismaClient, PropertyType } from "../../generated/prisma";

/**
 * Seed property metadata for all material families
 * @param prisma - The Prisma client instance
 */
export async function seedPropertyMetadata(prisma: PrismaClient) {
  const propertyMetadata = [
    { propertyKey: "mfiAv", description: "Melt Flow Index Average", unit: "g/10min", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "densityAv", description: "Density Average", unit: "g/cm³", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "tensileModulusAv", description: "Tensile Modulus Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "flexuralModulusAv", description: "Flexural Modulus Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "stressBreakAv", description: "Stress at Break Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "stressYieldAv", description: "Stress at Yield Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "nominalStrainBreakAv", description: "Nominal Strain at Break Average", unit: "%", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "notchedIzodAv", description: "Notched Izod Impact Average", unit: "kJ/m²", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "unnotchedIzodAv", description: "Unnotched Izod Impact Average", unit: "kJ/m²", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "hdtBAv", description: "Heat Deflection Temperature (Method B) Average", unit: "°C", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },
    { propertyKey: "cma23EnergyPunctureAv", description: "CMA 23°C Energy at Puncture Average", unit: "J", type: PropertyType.NUMBER, family: MaterialFamily.POLYMERS },

    { propertyKey: "bet", description: "BET Surface Area", unit: "m²/g", type: PropertyType.NUMBER, family: MaterialFamily.FILLERS },
    { propertyKey: "d50", description: "Median Particle Size", unit: "μm", type: PropertyType.NUMBER, family: MaterialFamily.FILLERS },
    { propertyKey: "d95d05", description: "Particle Size Distribution (D95/D05)", unit: "μm", type: PropertyType.NUMBER, family: MaterialFamily.FILLERS },
    { propertyKey: "whiteness", description: "Whiteness Index", unit: "%", type: PropertyType.NUMBER, family: MaterialFamily.FILLERS },
    { propertyKey: "form", description: "Physical Form", unit: null, type: PropertyType.STRING, family: MaterialFamily.FILLERS },
    { propertyKey: "codeanonymFiller", description: "Anonymized Filler Code", unit: null, type: PropertyType.STRING, family: MaterialFamily.FILLERS },

    { propertyKey: "density", description: "Density", unit: "g/cm³", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "mfi190216", description: "MFI at 190°C/2.16kg", unit: "g/10min", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "mfi230216", description: "MFI at 230°C/2.16kg", unit: "g/10min", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "nIzod23", description: "Notched Izod at 23°C", unit: "kJ/m²", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "flexModulus", description: "Flexural Modulus", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "tracModulus100", description: "Tensile Modulus at 100%", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "elongAtBreak", description: "Elongation at Break", unit: "%", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "meltingPoint", description: "Melting Point", unit: "°C", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "hdtB", description: "Heat Deflection Temperature B", unit: "°C", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "hdtA", description: "Heat Deflection Temperature A", unit: "°C", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "shoreA", description: "Shore A Hardness", unit: "Shore A", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "shoreD", description: "Shore D Hardness", unit: "Shore D", type: PropertyType.NUMBER, family: MaterialFamily.ELASTOMERS },
    { propertyKey: "codeanonymElasto", description: "Anonymized Elastomer Code", unit: null, type: PropertyType.STRING, family: MaterialFamily.ELASTOMERS },

    { propertyKey: "anonymizationCode", description: "Anonymization Code", unit: null, type: PropertyType.STRING, family: MaterialFamily.ADDITIVES },

    { propertyKey: "technicalProfileAvgValue", description: "Technical Profile Average Value", unit: null, type: PropertyType.STRING, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "pirPcrElv", description: "PIR/PCR/ELV Classification", unit: null, type: PropertyType.STRING, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "wasteDetails", description: "Waste Details", unit: null, type: PropertyType.STRING, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "filtrationSize", description: "Filtration Size", unit: "μm", type: PropertyType.STRING, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "levelOfPollution", description: "Level of Pollution", unit: null, type: PropertyType.STRING, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "mfiAv", description: "Melt Flow Index Average", unit: "g/10min", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "ashContentAv", description: "Ash Content Average", unit: "%", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "densityAv", description: "Density Average", unit: "g/cm³", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "tensileModulusAv", description: "Tensile Modulus Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "nominalStrainBreakAv", description: "Nominal Strain at Break Average", unit: "%", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "stressBreakAv", description: "Stress at Break Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "stressYieldAv", description: "Stress at Yield Average", unit: "MPa", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "notchedIzodAv", description: "Notched Izod Impact Average", unit: "kJ/m²", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "hdtBAv", description: "Heat Deflection Temperature (Method B) Average", unit: "°C", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "odorAv", description: "Odor Average", unit: null, type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
    { propertyKey: "voc", description: "Volatile Organic Compounds", unit: "μg/g", type: PropertyType.NUMBER, family: MaterialFamily.RECYCLE_POLYMERS },
  ];

  await prisma.propertyMetadata.createMany({
    data: propertyMetadata,
    skipDuplicates: true,
  });
}
