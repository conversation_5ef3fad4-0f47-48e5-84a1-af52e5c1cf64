import { PrismaClient } from "../../generated/prisma";

interface Users {
  omar: { id: string }
  nolan: { id: string }
  roger: { id: string }
}

interface Materials {
  polymers: {
    polymerA: { id: string }
    polymerB: { id: string }
  }
  recyclePolymers: {
    recyclePolymer1: { id: string }
    recyclePolymer2: { id: string }
    recyclePolymer3: { id: string }
  }
  fillers: {
    mineralFiller1: { id: string }
    mineralFiller2: { id: string }
  }
  elastomers: {
    elastomer: { id: string }
  }
  additives: {
    antioxidant1: { id: string }
    antioxidant2: { id: string }
    pigment: { id: string }
    antiscratch: { id: string }
    antiuv: { id: string }
  }
}

/**
 * Seed formulations, test results, and specifications
 * @param prisma - The Prisma client instance
 * @param users - The users object containing user IDs
 * @param materials - The materials object containing material IDs
 */
export async function seedFormulationsAndTests(prisma: PrismaClient, users: Users, materials: Materials) {
  const formulation1 = await prisma.formulation.create({
    data: {
      code: "BMP-001",
      name: "<PERSON>umper",
      grade: "A",
      ownerId: users.omar.id,
    },
  });

  const formulation2 = await prisma.formulation.create({
    data: {
      code: "BMP-002",
      name: "Bumper",
      grade: "B",
      ownerId: users.nolan.id,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: formulation1.id,
      materialId: materials.polymers.polymerA.id,
      percentage: 70,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: formulation1.id,
      materialId: materials.recyclePolymers.recyclePolymer1.id,
      percentage: 30,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: formulation2.id,
      materialId: materials.polymers.polymerB.id,
      percentage: 50,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: formulation2.id,
      materialId: materials.recyclePolymers.recyclePolymer2.id,
      percentage: 50,
    },
  });

  await prisma.request.create({
    data: {
      requesterId: users.omar.id,
      formulationId: formulation2.id,
      status: "PENDING_APPROVAL",
    },
  });

  await prisma.request.create({
    data: {
      requesterId: users.nolan.id,
      formulationId: formulation1.id,
      status: "APPROVED",
    },
  });

  await prisma.request.create({
    data: {
      requesterId: users.roger.id,
      formulationId: formulation1.id,
      status: "REJECTED",
    },
  });

  const compoundA = await prisma.formulation.create({
    data: {
      code: "CMP-001",
      name: "Compound",
      grade: "A",
      ownerId: users.omar.id,
    },
  });

  const compoundB = await prisma.formulation.create({
    data: {
      code: "CMP-002",
      name: "Compound",
      grade: "B",
      ownerId: users.nolan.id,
    },
  });

  const compoundC = await prisma.formulation.create({
    data: {
      code: "CMP-003",
      name: "Compound",
      grade: "C",
      ownerId: users.roger.id,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.polymers.polymerA.id,
      percentage: 20,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.recyclePolymers.recyclePolymer1.id,
      percentage: 40,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.fillers.mineralFiller1.id,
      percentage: 17,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.fillers.mineralFiller2.id,
      percentage: 5,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.elastomers.elastomer.id,
      percentage: 10,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.additives.antioxidant1.id,
      percentage: 2,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.additives.antioxidant2.id,
      percentage: 1,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.additives.pigment.id,
      percentage: 2,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.additives.antiscratch.id,
      percentage: 1,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundA.id,
      materialId: materials.additives.antiuv.id,
      percentage: 2,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.polymers.polymerA.id,
      percentage: 20,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.polymers.polymerB.id,
      percentage: 20,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.recyclePolymers.recyclePolymer1.id,
      percentage: 40,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.recyclePolymers.recyclePolymer2.id,
      percentage: 0,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.recyclePolymers.recyclePolymer3.id,
      percentage: 0,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.fillers.mineralFiller1.id,
      percentage: 17,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.fillers.mineralFiller2.id,
      percentage: 5,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.elastomers.elastomer.id,
      percentage: 10,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.additives.antioxidant1.id,
      percentage: 2,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.additives.antioxidant2.id,
      percentage: 1,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.additives.pigment.id,
      percentage: 2,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.additives.antiscratch.id,
      percentage: 1,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundB.id,
      materialId: materials.additives.antiuv.id,
      percentage: 2,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundC.id,
      materialId: materials.polymers.polymerA.id,
      percentage: 20,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundC.id,
      materialId: materials.recyclePolymers.recyclePolymer1.id,
      percentage: 40,
    },
  });

  await prisma.formulationMaterial.create({
    data: {
      formulationId: compoundC.id,
      materialId: materials.recyclePolymers.recyclePolymer2.id,
      percentage: 20,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundA.id,
      testName: "Tensile modulus (MPa)",
      propertyName: "tensileModulus",
      standard: "ISO 527-2",
      condition: "at 23°C",
      value: 1200,
      minRange: 1100,
      maxRange: 1360,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundA.id,
      testName: "MFI (g/10min)",
      propertyName: "mfi",
      standard: "ISO 1133",
      condition: "230°C 2,16kg",
      value: 24,
      minRange: 22.5,
      maxRange: 27.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundA.id,
      testName: "Notched Izod (kJ/m²)",
      propertyName: "nIzod",
      standard: "ISO 527",
      condition: "at 23°C, V Notch",
      value: 25.2,
      minRange: 21.6,
      maxRange: 28.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundA.id,
      testName: "Ash content (%)",
      propertyName: "ashContent",
      standard: "ISO 3451-1",
      value: 25.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundA.id,
      testName: "Density",
      propertyName: "density",
      standard: "ISO 1183",
      value: 1.025,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundA.id,
      testName: "Price (€/ton)",
      propertyName: "price",
      value: 1211,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundB.id,
      testName: "Tensile modulus (MPa)",
      propertyName: "tensileModulus",
      standard: "ISO 527-2",
      condition: "at 23°C",
      value: 1200,
      minRange: 1100,
      maxRange: 1360,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundB.id,
      testName: "MFI (g/10min)",
      propertyName: "mfi",
      standard: "ISO 1133",
      condition: "230°C 2,16kg",
      value: 24,
      minRange: 22.5,
      maxRange: 27.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundB.id,
      testName: "Notched Izod (kJ/m²)",
      propertyName: "nIzod",
      standard: "ISO 527",
      condition: "at 23°C, V Notch",
      value: 25.2,
      minRange: 21.6,
      maxRange: 28.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundB.id,
      testName: "Ash content (%)",
      propertyName: "ashContent",
      standard: "ISO 3451-1",
      value: 25.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundB.id,
      testName: "Density",
      propertyName: "density",
      standard: "ISO 1183",
      value: 1.025,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundB.id,
      testName: "Price (€/ton)",
      propertyName: "price",
      value: 1211,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundC.id,
      testName: "Tensile modulus (MPa)",
      propertyName: "tensileModulus",
      standard: "ISO 527-2",
      condition: "at 23°C",
      value: 1200,
      minRange: 1100,
      maxRange: 1360,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundC.id,
      testName: "MFI (g/10min)",
      propertyName: "mfi",
      standard: "ISO 1133",
      condition: "230°C 2,16kg",
      value: 24,
      minRange: 22.5,
      maxRange: 27.1,
    },
  });

  await prisma.testResult.create({
    data: {
      formulationId: compoundC.id,
      testName: "Notched Izod (kJ/m²)",
      propertyName: "nIzod",
      standard: "ISO 527",
      condition: "at 23°C, V Notch",
      value: 25.2,
      minRange: 21.6,
      maxRange: 28.1,
    },
  });

  await prisma.specification.create({
    data: {
      property: "tensileModulus",
      label: "Tensile Modulus",
      unit: "MPa",
      type: "RANGE",
    },
  });

  await prisma.specification.create({
    data: {
      property: "density",
      label: "Density",
      unit: "g/cm³",
      type: "RANGE",
    },
  });

  await prisma.specification.create({
    data: {
      property: "nIzod",
      label: "Impact Resistance",
      unit: "kJ/m²",
      type: "RANGE",
    },
  });

  await prisma.specification.create({
    data: {
      property: "mfi",
      label: "Melt Flow Index",
      unit: "g/10min",
      type: "RANGE",
    },
  });

  await prisma.specification.create({
    data: {
      property: "ashContent",
      label: "Ash Content",
      unit: "%",
      type: "RANGE",
    },
  });

  await prisma.specification.create({
    data: {
      property: "price",
      label: "Price",
      unit: "€/ton",
      type: "RANGE",
    },
  });
}
