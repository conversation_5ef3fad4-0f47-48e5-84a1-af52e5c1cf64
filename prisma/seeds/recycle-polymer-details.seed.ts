import { PrismaClient } from "../../generated/prisma";

interface Materials {
  recyclePolymers: {
    recyclePolymer1: { id: string }
    recyclePolymer2: { id: string }
    recyclePolymer3: { id: string }
  }
}

/**
 * Seed detailed recycle polymer properties and test data
 * @param prisma - The Prisma client instance
 * @param materials - The materials object containing recycle polymer IDs
 */
export async function seedRecyclePolymerDetails(prisma: PrismaClient, materials: Materials) {
  await prisma.recyclePolymer.create({
    data: {
      technicalProfileAvgValue: "Example Profile 1",
      resultUpdateDate: new Date("2025-06-02"),
      validationEngineeringAndFeedstockOrUseInCompounds: new Date("2025-06-02"),
      color: "Light Grey",
      pirPcrElv: "PCR",
      wasteDetails: "Plastic granules",
      materialForm: "Granules",
      tds: "Available",
      productVolumesKtY: 10,
      volumesAvailableForMactKtY: 6,
      priceExw: 620,
      priceExwDate: "2025-06-02",
      certificatesReachRohs: "Reach",
      endOfWasteStatus: "No",
      comment: "Recycle polymer material 2",
      filtrationLocation: "Plant A",
      systemFiltration: "Cartridge filter",
      filtrationSize: "150µ",
      quantityFilteredRemaining: 10,
      d22NbFiltersUsed: 3,
      d22NbFilterPerKgFeedstock: 0.12,
      d32QuantityScrap: 4,
      d32NbFilterPerKgFeedstock: 0.04,
      levelOfPollution: "Medium",
      venting: "No",
      trialDate: new Date("2025-05-20"),
      vacuumPressure: "90mbar",
      screwSpeed: "110 rpm",
      screwProfile: "Custom",
      mfiNorme: "ISO 1133",
      mfiTestConditions: "230°C 2.16kg",
      mfiAv: 23,
      mfiStdDv: 1.3,
      ashContentNorme: "ISO 3451-1",
      ashContentAv: 0.6,
      ashContentStdDv: 0.12,
      densityNorme: "ISO 1183",
      densityAv: 0.92,
      densityStdDv: 0.006,
      tensileModulusNorme: "ISO 527",
      tensileModulusConditions: "23°C",
      tensileModulusAv: 1180,
      tensileModulusStdDv: 45,
      flexuralModulusNorme: "ISO 178",
      flexuralModulusAv: 1480,
      flexuralModulusStdDev: 55,
      flexuralStressFcAv: 38,
      flexuralStressFcStdDev: 1.8,
      stressBreakNorme: "ISO 527",
      stressBreakAv: 28,
      stressBreakStdDv: 1.3,
      stressYieldAv: 24,
      stressYieldStdDv: 0.9,
      yieldStrainAv: 4.8,
      yieldStrainStdDv: 0.18,
      strainBreakNorme: "ISO 527",
      strainBreakAv: 9.5,
      strainBreakStdDv: 0.45,
      nominalStrainBreakAv: 8.5,
      nominalStrainBreakStdDv: 0.35,
      notchedIzodNorme: "ISO 180",
      notchedIzodAv: 19,
      notchedIzodStdDv: 0.9,
      notchedIzodFailureType: "V-Notch",
      unnotchedIzodNorme: "ISO 180",
      unnotchedIzodAv: 21,
      unnotchedIzodStdDv: 1.1,
      unnotchedIzodFailureType: "No notch",
      hdtBNorme: "ISO 75",
      hdtBAv: 88,
      hdtBStdDv: 2.5,
      odorNorme: "ISO 12219",
      odorNote1: "Mild",
      odorNote2: "Slightly sweet",
      odorNote3: "",
      odorAv: 1.8,
      odorStdv: 0.09,
      vocFogNorme: "ISO 16000",
      voc: 0.45,
      voc2: 0.28,
      fog: 0.18,
      l: 0.9,
      a: 0.4,
      b: 0.25,
      cma23Norm: "ISO 527",
      cma23V: 6.3,
      cma23NbSamples: 9,
      cma23MeanBreakType: "Brittle",
      cma23EnergyForceMaxAv: 14,
      cma23EnergyForceMaxStdDev: 0.9,
      cma23EnergyPunctureAv: 11,
      cma23EnergyPunctureStdDev: 0.7,
      cma0Norm: "ISO 527",
      cma0V: 5.8,
      cma0NbSamples: 7,
      cma0MeanBreakType: "Ductile",
      cma0EnergyForceMaxAv: 13,
      cma0EnergyForceMaxStdDev: 1,
      cma0EnergyPunctureAv: 10,
      cma0EnergyPunctureStdDev: 0.6,
      cma10Norm: "ISO 527",
      cma10V: 6,
      cma10NbSamples: 8,
      cma10MeanBreakType: "Brittle",
      cma10EnergyForceMaxAv: 12,
      cma10EnergyForceMaxStdDev: 0.8,
      cma10EnergyPunctureAv: 9,
      cma10EnergyPunctureStdDev: 0.5,
      cma20Norm: "ISO 527",
      cma20V: 6.1,
      cma20NbSamples: 6,
      cma20MeanBreakType: "Ductile",
      cma20EnergyForceMaxAv: 15,
      cma20EnergyForceMaxStdDev: 1.1,
      cma20EnergyPunctureAv: 12,
      cma20EnergyPunctureStdDev: 0.8,
      cma30Norm: "ISO 527",
      cma30V: 5.9,
      cma30NbSamples: 5,
      cma30MeanBreakType: "Brittle",
      cma30EnergyForceMaxAv: 13,
      cma30EnergyForceMaxStdDev: 0.8,
      cma30EnergyPunctureAv: 10,
      cma30EnergyPunctureStdDev: 0.6,
      material: {
        connect: { id: materials.recyclePolymers.recyclePolymer1.id },
      },
    },
  });

  await prisma.recyclePolymer.create({
    data: {
      technicalProfileAvgValue: "Example Profile 2",
      resultUpdateDate: new Date("2025-06-02"),
      validationEngineeringAndFeedstockOrUseInCompounds: new Date("2025-06-02"),
      color: "Light Grey",
      pirPcrElv: "PCR",
      wasteDetails: "Plastic granules",
      materialForm: "Granules",
      tds: "Available",
      productVolumesKtY: 8.5,
      volumesAvailableForMactKtY: 6,
      priceExw: 620,
      priceExwDate: "2025-06-02",
      certificatesReachRohs: "Reach",
      endOfWasteStatus: "No",
      comment: "Recycle polymer material 2",
      filtrationLocation: "Plant B",
      systemFiltration: "Cartridge filter",
      filtrationSize: "150µ",
      quantityFilteredRemaining: 10,
      d22NbFiltersUsed: 3,
      d22NbFilterPerKgFeedstock: 0.12,
      d32QuantityScrap: 4,
      d32NbFilterPerKgFeedstock: 0.04,
      levelOfPollution: "Medium",
      venting: "No",
      trialDate: new Date("2025-05-20"),
      vacuumPressure: "90mbar",
      screwSpeed: "110 rpm",
      screwProfile: "Custom",
      mfiNorme: "ISO 1133",
      mfiTestConditions: "230°C 2.16kg",
      mfiAv: 23,
      mfiStdDv: 1.3,
      ashContentNorme: "ISO 3451-1",
      ashContentAv: 0.6,
      ashContentStdDv: 0.12,
      densityNorme: "ISO 1183",
      densityAv: 0.92,
      densityStdDv: 0.006,
      tensileModulusNorme: "ISO 527",
      tensileModulusConditions: "23°C",
      tensileModulusAv: 1180,
      tensileModulusStdDv: 45,
      flexuralModulusNorme: "ISO 178",
      flexuralModulusAv: 1480,
      flexuralModulusStdDev: 55,
      flexuralStressFcAv: 38,
      flexuralStressFcStdDev: 1.8,
      stressBreakNorme: "ISO 527",
      stressBreakAv: 28,
      stressBreakStdDv: 1.3,
      stressYieldAv: 24,
      stressYieldStdDv: 0.9,
      yieldStrainAv: 4.8,
      yieldStrainStdDv: 0.18,
      strainBreakNorme: "ISO 527",
      strainBreakAv: 9.5,
      strainBreakStdDv: 0.45,
      nominalStrainBreakAv: 8.5,
      nominalStrainBreakStdDv: 0.35,
      notchedIzodNorme: "ISO 180",
      notchedIzodAv: 19,
      notchedIzodStdDv: 0.9,
      notchedIzodFailureType: "V-Notch",
      unnotchedIzodNorme: "ISO 180",
      unnotchedIzodAv: 21,
      unnotchedIzodStdDv: 1.1,
      unnotchedIzodFailureType: "No notch",
      hdtBNorme: "ISO 75",
      hdtBAv: 88,
      hdtBStdDv: 2.5,
      odorNorme: "ISO 12219",
      odorNote1: "Mild",
      odorNote2: "Slightly sweet",
      odorNote3: "",
      odorAv: 1.8,
      odorStdv: 0.09,
      vocFogNorme: "ISO 16000",
      voc: 0.45,
      voc2: 0.28,
      fog: 0.18,
      l: 0.9,
      a: 0.4,
      b: 0.25,
      cma23Norm: "ISO 527",
      cma23V: 6.3,
      cma23NbSamples: 9,
      cma23MeanBreakType: "Brittle",
      cma23EnergyForceMaxAv: 14,
      cma23EnergyForceMaxStdDev: 0.9,
      cma23EnergyPunctureAv: 11,
      cma23EnergyPunctureStdDev: 0.7,
      cma0Norm: "ISO 527",
      cma0V: 5.8,
      cma0NbSamples: 7,
      cma0MeanBreakType: "Ductile",
      cma0EnergyForceMaxAv: 13,
      cma0EnergyForceMaxStdDev: 1,
      cma0EnergyPunctureAv: 10,
      cma0EnergyPunctureStdDev: 0.6,
      cma10Norm: "ISO 527",
      cma10V: 6,
      cma10NbSamples: 8,
      cma10MeanBreakType: "Brittle",
      cma10EnergyForceMaxAv: 12,
      cma10EnergyForceMaxStdDev: 0.8,
      cma10EnergyPunctureAv: 9,
      cma10EnergyPunctureStdDev: 0.5,
      cma20Norm: "ISO 527",
      cma20V: 6.1,
      cma20NbSamples: 6,
      cma20MeanBreakType: "Ductile",
      cma20EnergyForceMaxAv: 15,
      cma20EnergyForceMaxStdDev: 1.1,
      cma20EnergyPunctureAv: 12,
      cma20EnergyPunctureStdDev: 0.8,
      cma30Norm: "ISO 527",
      cma30V: 5.9,
      cma30NbSamples: 5,
      cma30MeanBreakType: "Brittle",
      cma30EnergyForceMaxAv: 13,
      cma30EnergyForceMaxStdDev: 0.8,
      cma30EnergyPunctureAv: 10,
      cma30EnergyPunctureStdDev: 0.6,
      material: {
        connect: { id: materials.recyclePolymers.recyclePolymer2.id },
      },
    },
  });

  await prisma.recyclePolymer.create({
    data: {
      technicalProfileAvgValue: "Example Profile 3",
      resultUpdateDate: new Date("2025-06-03"),
      validationEngineeringAndFeedstockOrUseInCompounds: new Date("2025-06-02"),
      color: "White",
      pirPcrElv: "ELV",
      wasteDetails: "Plastic pellets",
      materialForm: "Pellets",
      tds: "Available",
      productVolumesKtY: 12,
      volumesAvailableForMactKtY: 9,
      priceExw: 670,
      priceExwDate: "2025-06-03",
      certificatesReachRohs: "RoHS",
      endOfWasteStatus: "Yes",
      comment: "Recycle polymer material 3",
      filtrationLocation: "Plant C",
      systemFiltration: "Bag filter",
      filtrationSize: "180µ",
      quantityFilteredRemaining: 15,
      d22NbFiltersUsed: 1,
      d22NbFilterPerKgFeedstock: 0.08,
      d32QuantityScrap: 6,
      d32NbFilterPerKgFeedstock: 0.06,
      levelOfPollution: "Low",
      venting: "Yes",
      trialDate: new Date("2025-05-25"),
      vacuumPressure: "110mbar",
      screwSpeed: "130 rpm",
      screwProfile: "Advanced",
      mfiNorme: "ISO 1133",
      mfiTestConditions: "230°C 2.16kg",
      mfiAv: 26,
      mfiStdDv: 1.6,
      ashContentNorme: "ISO 3451-1",
      ashContentAv: 0.4,
      ashContentStdDv: 0.09,
      densityNorme: "ISO 1183",
      densityAv: 0.89,
      densityStdDv: 0.004,
      tensileModulusNorme: "ISO 527",
      tensileModulusConditions: "23°C",
      tensileModulusAv: 1220,
      tensileModulusStdDv: 55,
      flexuralModulusNorme: "ISO 178",
      flexuralModulusAv: 1520,
      flexuralModulusStdDev: 65,
      flexuralStressFcAv: 42,
      flexuralStressFcStdDev: 2.2,
      stressBreakNorme: "ISO 527",
      stressBreakAv: 32,
      stressBreakStdDv: 1.6,
      stressYieldAv: 27,
      stressYieldStdDv: 1.1,
      yieldStrainAv: 5.2,
      yieldStrainStdDv: 0.22,
      strainBreakNorme: "ISO 527",
      strainBreakAv: 11,
      strainBreakStdDv: 0.55,
      nominalStrainBreakAv: 10,
      nominalStrainBreakStdDv: 0.45,
      notchedIzodNorme: "ISO 180",
      notchedIzodAv: 21,
      notchedIzodStdDv: 1.1,
      notchedIzodFailureType: "V-Notch",
      unnotchedIzodNorme: "ISO 180",
      unnotchedIzodAv: 23,
      unnotchedIzodStdDv: 1.3,
      unnotchedIzodFailureType: "No notch",
      hdtBNorme: "ISO 75",
      hdtBAv: 92,
      hdtBStdDv: 3.2,
      odorNorme: "ISO 12219",
      odorNote1: "Neutral",
      odorNote2: "",
      odorNote3: "",
      odorAv: 1.5,
      odorStdv: 0.07,
      vocFogNorme: "ISO 16000",
      voc: 0.4,
      voc2: 0.25,
      fog: 0.15,
      l: 1.1,
      a: 0.6,
      b: 0.35,
      cma23Norm: "ISO 527",
      cma23V: 6.7,
      cma23NbSamples: 11,
      cma23MeanBreakType: "Brittle",
      cma23EnergyForceMaxAv: 16,
      cma23EnergyForceMaxStdDev: 1.1,
      cma23EnergyPunctureAv: 13,
      cma23EnergyPunctureStdDev: 0.9,
      cma0Norm: "ISO 527",
      cma0V: 6.2,
      cma0NbSamples: 9,
      cma0MeanBreakType: "Ductile",
      cma0EnergyForceMaxAv: 15,
      cma0EnergyForceMaxStdDev: 1.2,
      cma0EnergyPunctureAv: 12,
      cma0EnergyPunctureStdDev: 0.8,
      cma10Norm: "ISO 527",
      cma10V: 6.4,
      cma10NbSamples: 10,
      cma10MeanBreakType: "Brittle",
      cma10EnergyForceMaxAv: 14,
      cma10EnergyForceMaxStdDev: 1,
      cma10EnergyPunctureAv: 11,
      cma10EnergyPunctureStdDev: 0.7,
      cma20Norm: "ISO 527",
      cma20V: 6.5,
      cma20NbSamples: 8,
      cma20MeanBreakType: "Ductile",
      cma20EnergyForceMaxAv: 17,
      cma20EnergyForceMaxStdDev: 1.3,
      cma20EnergyPunctureAv: 14,
      cma20EnergyPunctureStdDev: 1,
      cma30Norm: "ISO 527",
      cma30V: 6.3,
      cma30NbSamples: 7,
      cma30MeanBreakType: "Brittle",
      cma30EnergyForceMaxAv: 15,
      cma30EnergyForceMaxStdDev: 1.1,
      cma30EnergyPunctureAv: 12,
      cma30EnergyPunctureStdDev: 0.8,
      material: {
        connect: { id: materials.recyclePolymers.recyclePolymer3.id },
      },
    },
  });
}
