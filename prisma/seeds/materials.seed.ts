import { MaterialFamily, PrismaClient } from "../../generated/prisma";

/**
 * Seed suppliers and materials data
 * @param prisma - The Prisma client instance
 * @returns Object containing created materials for use in other seed functions
 */
export async function seedMaterials(prisma: PrismaClient) {
  const supplierExmobile = await prisma.supplier.create({
    data: {
      name: "Exmobile",
    },
  });

  const supplierCibas = await prisma.supplier.create({
    data: {
      name: "Cibas",
    },
  });

  const supplierIG = await prisma.supplier.create({
    data: {
      name: "IG 8-01",
    },
  });

  const supplierRePolyTech = await prisma.supplier.create({
    data: {
      name: "RePolyTech",
    },
  });

  const polymerA = await prisma.material.create({
    data: {
      type: "Virgin PP Homopolymer",
      reference: "PP-H 1200",
      family: MaterialFamily.POLYMERS,
      supplierId: supplierExmobile.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
    },
  });

  await prisma.polymer.create({
    data: {
      materialId: polymerA.id,
      tensileModulusAv: 1250,
      notchedIzodAv: 26.5,
      mfiAv: 25.2,
      hdtBAv: 95.5,
      densityAv: 0.905,
    },
  });

  const polymerB = await prisma.material.create({
    data: {
      type: "Virgin PP Homopolymer",
      reference: "A100-80",
      family: MaterialFamily.POLYMERS,
      supplierId: supplierCibas.id,
      supplierBatchNumber: "BFC 3386",
      origin: "Asia",
    },
  });

  await prisma.polymer.create({
    data: {
      materialId: polymerB.id,
      tensileModulusAv: 1180,
      notchedIzodAv: 24.8,
      mfiAv: 26.8,
      hdtBAv: 92.3,
      densityAv: 0.902,
    },
  });

  const recyclePolymer1 = await prisma.material.create({
    data: {
      type: "Recycle PP Homopolymer",
      reference: "PlastiLoop",
      family: MaterialFamily.RECYCLE_POLYMERS,
      supplierId: supplierRePolyTech.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
    },
  });

  const recyclePolymer2 = await prisma.material.create({
    data: {
      type: "Recycle PP Homopolymer",
      reference: "Looplene",
      family: MaterialFamily.RECYCLE_POLYMERS,
      supplierId: supplierIG.id,
      supplierBatchNumber: "BFC 3380",
      origin: "North America",
    },
  });

  const recyclePolymer3 = await prisma.material.create({
    data: {
      type: "Recycle PP Homopolymer",
      reference: "EcoPoly+",
      family: MaterialFamily.RECYCLE_POLYMERS,
      supplierId: supplierCibas.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
    },
  });

  const mineralFiller1 = await prisma.material.create({
    data: {
      type: "Mineral Fillers",
      reference: "RAHT1000",
      family: MaterialFamily.FILLERS,
      supplierId: supplierExmobile.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
      filler: {
        create: {
          codeanonymFiller: "CF_RAHT1000",
          d50: 10.5,
          d95d05: 2.1,
          whiteness: 95.8,
          form: "Powder",
        },
      },
    },
  });

  const mineralFiller2 = await prisma.material.create({
    data: {
      type: "Mineral Fillers",
      reference: "ML40MCS",
      family: MaterialFamily.FILLERS,
      supplierId: supplierIG.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Asia",
      filler: {
        create: {
          codeanonymFiller: "CF_ML40MCS",
          d50: 8.2,
          d95d05: 1.9,
          whiteness: 92.3,
          form: "Granule",
        },
      },
    },
  });

  const elastomer = await prisma.material.create({
    data: {
      type: "Elastomer",
      reference: "INTERLACE4000",
      family: MaterialFamily.ELASTOMERS,
      supplierId: supplierRePolyTech.id,
      supplierBatchNumber: "BFC 3380",
      origin: "North America",
      elastomer: {
        create: {
          density: 0.88,
          mfi190216: 25,
          codeanonymElasto: "CE_INTERLACE4000",
          nIzod23: 65,
          flexModulus: 450,
          tracModulus100: 18.5,
          elongAtBreak: 450,
          mfi230216: 28.5,
          meltingPoint: 180,
          hdtB: 95,
          hdtA: 92,
          shoreA: 65,
          shoreD: 25,
        },
      },
    },
  });

  const antioxidant1 = await prisma.material.create({
    data: {
      type: "Antioxidant",
      reference: "Rigonax 069",
      family: MaterialFamily.ADDITIVES,
      supplierId: supplierExmobile.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
      additive: {
        create: {
          anonymizationCode: "ANT_001",
        },
      },
    },
  });

  const antioxidant2 = await prisma.material.create({
    data: {
      type: "Antioxidant",
      reference: "Gifrosa 60",
      family: MaterialFamily.ADDITIVES,
      supplierId: supplierIG.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
      additive: {
        create: {
          anonymizationCode: "ANT_002",
        },
      },
    },
  });

  const pigment = await prisma.material.create({
    data: {
      type: "Pigment",
      reference: "CC12306656887UD",
      family: MaterialFamily.ADDITIVES,
      supplierId: supplierCibas.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Europe",
      additive: {
        create: {
          anonymizationCode: "PIG_001",
        },
      },
    },
  });

  const antiscratch = await prisma.material.create({
    data: {
      type: "Antiscratch",
      reference: "W117",
      family: MaterialFamily.ADDITIVES,
      supplierId: supplierCibas.id,
      supplierBatchNumber: "BFC 3380",
      origin: "Asia",
      additive: {
        create: {
          anonymizationCode: "ANTI_001",
        },
      },
    },
  });

  const antiuv = await prisma.material.create({
    data: {
      type: "AntiUV",
      reference: "Octorylene",
      family: MaterialFamily.ADDITIVES,
      supplierId: supplierCibas.id,
      supplierBatchNumber: "BFC 3380",
      origin: "North America",
      additive: {
        create: {
          anonymizationCode: "UV_001",
        },
      },
    },
  });

  return {
    polymers: { polymerA, polymerB },
    recyclePolymers: { recyclePolymer1, recyclePolymer2, recyclePolymer3 },
    fillers: { mineralFiller1, mineralFiller2 },
    elastomers: { elastomer },
    additives: { antioxidant1, antioxidant2, pigment, antiscratch, antiuv },
  };
}
