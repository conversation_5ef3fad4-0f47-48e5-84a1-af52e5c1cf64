# Remaining Linter Issues

## Summary
Current status: **479 remaining lint problems** across **20 files**

## Progress Made
- ✅ Fixed Request DTO files (split into separate files to resolve max-classes-per-file)
- ✅ Fixed Request repository sort-keys issues  
- ✅ Added accessibility modifiers to search query builder
- ✅ **COMPLETED: Request controller spec file** (fixed sort-keys and enum issues)
- ✅ **COMPLETED: Request controller file** (fixed all ApiOperation/ApiResponse sort-keys, nullish coalescing)
- ✅ **Major Progress**: Reduced from 541 to 479 problems (62 issues resolved)
- ✅ **Files Fixed**: Reduced from 25 to 20 files with issues (5 files fully fixed)

## Files with Lint Issues

### Request Module (2 remaining)
- src/request/request.service.spec.ts  
- src/request/request.service.ts

### Role Module (3 files)
- src/role/repositories/role.repository.ts
- src/role/role.service.spec.ts
- src/role/role.types.ts

### Specification Module (9 files)
- src/specification/dto/paginated-specification-response.dto.ts
- src/specification/dto/specification-response.dto.ts
- src/specification/repositories/specification.repository.spec.ts
- src/specification/repositories/specification.repository.ts
- src/specification/specification.controller.spec.ts
- src/specification/specification.controller.ts
- src/specification/specification.module.ts
- src/specification/specification.service.spec.ts
- src/specification/specification.service.ts

### User Module (6 files)
- src/user/dto/create-user.dto.ts
- src/user/dto/paginated-user-response.dto.ts
- src/user/dto/user-response.dto.ts
- src/user/repositories/user.repository.spec.ts
- src/user/repositories/user.repository.ts
- src/user/user.controller.spec.ts

## Common Issue Types Expected

Based on the files listed, the remaining issues likely include:

### 1. TypeScript Safety Issues
- Unsafe `any` type operations
- Missing type definitions
- Unsafe property access

### 2. Code Organization Issues  
- Missing accessibility modifiers (`public`/`private`)
- Naming convention violations
- Import/export organization

### 3. Code Style Issues
- Object key sorting requirements
- Quote style inconsistencies
- Formatting inconsistencies

### 4. Test-Specific Issues
- Type safety in test files (*.spec.ts)
- Mock object configurations
- Test data structures

## Recommended Approach

1. **Prioritize by module** - Fix one module at a time for better organization
2. **Focus on safety first** - Address TypeScript safety issues before style issues  
3. **Batch similar fixes** - Group similar types of fixes across multiple files
4. **Test after each module** - Ensure functionality isn't broken after fixes

## Notes
- Total of 61 files need attention across all modules
- Mix of service files, DTOs, repositories, controllers, and tests
- Repository and query builder files likely have the most complex issues
- Some guard files are included and may need special handling