[{"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/app.controller.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/app.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/app.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/auth/auth.guard.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/auth/auth.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/auth/auth.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/auth/role.guard.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/criteria-operator.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/error-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/paginated-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/pagination-meta.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/pagination-query.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/dto/user.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/interfaces/authenticated-request.interface.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/interfaces/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/utils/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/utils/pagination.utility.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/common/utils/string.utility.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/department/department.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/department/department.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/department/dto/department-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/department/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/department/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/department/repositories/department.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/email/email.constants.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/email/email.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/email/email.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/comparison.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/comparison-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/comparison-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/create-formulation-material.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/create-formulation-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/create-formulation-test-result.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/create-formulation.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/create-simulation-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/create-simulation.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/created-formulation.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/criteria.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/formulation-comparison.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/formulation-filter.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/formulation-material.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/formulation-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/formulation-test-result.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/material-criteria.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/material-in-formulation.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/paginated-formulation-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/simulation/base-simulation-material.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/simulation/request-simulation-grade.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/simulation/request-simulation-material.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/simulation/response-simulation-material.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/simulation/response-simulation-test-grade.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/simulation/response-simulation-test-result.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/test-result-grade-value.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/dto/test-result.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/enum/access-status.enum.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/formulation.controller.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/formulation.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/formulation.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/repositories/comparison.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/repositories/criteria-query.builder.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/repositories/formulation.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/repositories/test-result.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/formulation/utils/formulation-access.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/instrumentation.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/location/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/location/dto/location-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/location/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/location/location.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/location/location.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/location/repositories/location.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/main.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/batch-update-material-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/batch-update-material-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-criteria.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/additive.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/base-polymer.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/elastomer.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/filler.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/polymer.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/family-data/recycle-polymer.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-comparison-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-comparison-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-comparison.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-export-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-filter-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-filter-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-page-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-search-page-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-search-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/material-search-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/paginated-material-page-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/paginated-material-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/property-metadata.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/dto/update-material.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/enum/material-filter-column.enum.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/events/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/events/material-status-changed.event.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/material-comparison.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/material-export.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/material.controller.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/material.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/material.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/repositories/material-comparison.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/repositories/material-filter.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/repositories/material.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/repositories/property-metadata.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/services/property-metadata.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/utils/export-columns.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/utils/family-property.validator.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/utils/filter-condition.builder.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/utils/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/material/validators/family-data.validator.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/dto/mark-as-read.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/dto/notification-query.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/dto/notification-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/dto/paginated-notification-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/notification.controller.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/notification.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/notification.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/repositories/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/notification/repositories/notification.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/prisma.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/create-request.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/department.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/formulation.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/location.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/paginated-request-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/request-action.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/request-action.enum.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/request-query.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/request-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/dto/requester.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/events/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/events/new-formulation-request.event.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/events/request-status-changed.event.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/repositories/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/repositories/request.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/repositories/search-query.builder.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/request.controller.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/request.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/request/request.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/dto/role-response.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/repositories/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/repositories/role.repository.ts", "messages": [{"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition constructor.", "line": 11, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 11, "endColumn": 14, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [201, 201], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [201, 201], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [201, 201], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findById.", "line": 13, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 13, "endColumn": 17, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [259, 259], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [259, 259], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [259, 259], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition getRoleNames.", "line": 19, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 19, "endColumn": 21, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [391, 391], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [391, 391], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [391, 391], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'orderBy' should be before 'select'.", "line": 22, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 22, "endColumn": 14}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findAll.", "line": 27, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 27, "endColumn": 16, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [603, 603], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [603, 603], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [603, 603], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findByCode.", "line": 33, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 33, "endColumn": 19, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [728, 728], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [728, 728], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [728, 728], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Injectable } from \"@nestjs/common\";\nimport { PrismaService } from \"@/prisma.service\";\n\nexport interface RoleData {\n  id: string\n  name: string\n}\n\n@Injectable()\nexport class RoleRepository {\n  constructor(private readonly prisma: PrismaService) {}\n\n  async findById(id: string): Promise<RoleData | null> {\n    return this.prisma.role.findUnique({\n      where: { id },\n    });\n  }\n\n  async getRoleNames(): Promise<string[]> {\n    const roles = await this.prisma.role.findMany({\n      select: { name: true },\n      orderBy: { name: \"asc\" },\n    });\n    return roles.map(role => role.name);\n  }\n\n  async findAll(): Promise<RoleData[]> {\n    return this.prisma.role.findMany({\n      orderBy: { name: \"asc\" },\n    });\n  }\n\n  async findByCode(code: string): Promise<RoleData | null> {\n    return this.prisma.role.findUnique({\n      where: { code },\n    });\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/role.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/role.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/role/role.types.ts", "messages": [{"ruleId": "@typescript-eslint/naming-convention", "severity": 2, "message": "Enum name `UserRole` must match one of the following formats: UPPER_CASE", "line": 1, "column": 13, "nodeType": "Identifier", "messageId": "doesNotMatchFormat", "endLine": 1, "endColumn": 21}], "suppressedMessages": [], "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "export enum UserRole {\n  ADMIN = \"ADMIN\",\n  DATA_SCIENTIST = \"DATA_SCIENTIST\",\n  ENGINEERING_MANAGER = \"ENGINEERING_MANAGER\",\n  ENGINEERS = \"ENGINEERS\",\n  FEEDSTOCK_RECYCLING_MEMBERS = \"FEEDSTOCK_RECYCLING_MEMBERS\",\n  MATERIAL_MANAGER = \"MATERIAL_MANAGER\",\n  UNKNOWN = \"UNKNOWN\",\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/dto/paginated-specification-response.dto.ts", "messages": [{"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property data.", "line": 7, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 7, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [346, 346], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [346, 346], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [346, 346], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property meta.", "line": 10, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 10, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [463, 463], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [463, 463], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [463, 463], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { ApiProperty } from \"@nestjs/swagger\";\nimport { PaginationMetaDto } from \"../../common/dto/pagination-meta.dto\";\nimport { SpecificationResponseDto } from \"./specification-response.dto\";\n\nexport class PaginatedSpecificationResponseDto {\n  @ApiProperty({ description: \"Array of specification objects\", type: [SpecificationResponseDto] })\n  data: SpecificationResponseDto[];\n\n  @ApiProperty({ description: \"Pagination metadata\", type: PaginationMetaDto })\n  meta: PaginationMetaDto;\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/dto/specification-response.dto.ts", "messages": [{"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'enum' should be before 'example'.", "line": 33, "column": 5, "nodeType": "Property", "messageId": "sortKeys", "endLine": 33, "endColumn": 9}], "suppressedMessages": [], "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { ApiProperty } from \"@nestjs/swagger\";\nimport { SpecificationType } from \"@/generated/prisma\";\n\nexport class SpecificationResponseDto {\n  @ApiProperty({\n    description: \"Unique identifier of the specification\",\n    example: \"bf60be02-876f-48d3-875e-a79a2ccf3372\",\n  })\n  public id: string;\n\n  @ApiProperty({\n    description: \"Property name\",\n    example: \"mfi\",\n  })\n  public property: string;\n\n  @ApiProperty({\n    description: \"Human-readable label for the property\",\n    example: \"Melt Flow Index\",\n  })\n  public label: string;\n\n  @ApiProperty({\n    description: \"Unit of measurement\",\n    example: \"g/10min\",\n    required: false,\n  })\n  public unit: string | null;\n\n  @ApiProperty({\n    description: \"Type of specification - range for numeric ranges, text for text input\",\n    example: \"RANGE\",\n    enum: SpecificationType,\n  })\n  public type: SpecificationType;\n\n  @ApiProperty({\n    description: \"Creation date\",\n    example: \"2025-06-25T16:53:48.014Z\",\n  })\n  public createdAt: Date;\n\n  @ApiProperty({\n    description: \"Last update date\",\n    example: \"2025-06-25T16:53:48.014Z\",\n  })\n  public updatedAt: Date;\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/repositories/specification.repository.ts", "messages": [{"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition constructor.", "line": 33, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 33, "endColumn": 14, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [688, 688], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [688, 688], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [688, 688], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findAll.", "line": 35, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 35, "endColumn": 16, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [746, 746], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [746, 746], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [746, 746], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 2, "message": "Unexpected any. Specify a different type.", "line": 39, "column": 18, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 39, "endColumn": 21, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [976, 979], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [976, 979], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/no-unsafe-member-access", "severity": 2, "message": "Unsafe member access .OR on an `any` value.", "line": 42, "column": 13, "nodeType": "Identifier", "messageId": "unsafeMemberExpression", "endLine": 42, "endColumn": 15}, {"ruleId": "@typescript-eslint/no-unsafe-assignment", "severity": 2, "message": "Unsafe assignment of an `any` value.", "line": 50, "column": 9, "nodeType": "Property", "messageId": "anyAssignment", "endLine": 50, "endColumn": 14}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'skip' should be before 'where'.", "line": 51, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 51, "endColumn": 13}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'orderBy' should be before 'take'.", "line": 53, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 53, "endColumn": 16}, {"ruleId": "@typescript-eslint/no-unsafe-assignment", "severity": 2, "message": "Unsafe assignment of an `any` value.", "line": 57, "column": 41, "nodeType": "Property", "messageId": "anyAssignment", "endLine": 57, "endColumn": 46}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'page' should be before 'total'.", "line": 63, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 63, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'limit' should be before 'page'.", "line": 64, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 64, "endColumn": 12}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findById.", "line": 68, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 68, "endColumn": 17, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [1570, 1570], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [1570, 1570], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [1570, 1570], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Injectable } from \"@nestjs/common\";\nimport { calculatePaginationOffset } from \"../../common/utils\";\nimport { PrismaService } from \"../../prisma.service\";\nimport type { Specification } from \"@/generated/prisma\";\n\nexport interface CreateSpecificationData {\n  property: string\n  label: string\n  unit?: string\n}\n\nexport interface UpdateSpecificationData {\n  property?: string\n  label?: string\n  unit?: string\n}\n\nexport interface SpecificationFilters {\n  search?: string\n  page?: number\n  limit?: number\n}\n\nexport interface PaginatedSpecificationResult {\n  data: Specification[]\n  total: number\n  page: number\n  limit: number\n}\n\n@Injectable()\nexport class SpecificationRepository {\n  constructor(private readonly prisma: PrismaService) {}\n\n  async findAll(filters: SpecificationFilters): Promise<PaginatedSpecificationResult> {\n    const { page = 1, limit = 10, ...filterParameters } = filters;\n    const offset = calculatePaginationOffset(page, limit);\n\n    const where: any = {};\n\n    if (filterParameters.search) {\n      where.OR = [\n        { label: { contains: filterParameters.search, mode: \"insensitive\" } },\n        { property: { contains: filterParameters.search, mode: \"insensitive\" } },\n      ];\n    }\n\n    const [data, total] = await Promise.all([\n      this.prisma.specification.findMany({\n        where,\n        skip: offset,\n        take: limit,\n        orderBy: {\n          updatedAt: \"desc\",\n        },\n      }),\n      this.prisma.specification.count({ where }),\n    ]);\n\n    return {\n      data,\n      total,\n      page,\n      limit,\n    };\n  }\n\n  async findById(id: string): Promise<Specification | null> {\n    return this.prisma.specification.findUnique({\n      where: { id },\n    });\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/specification.controller.ts", "messages": [{"ruleId": "@typescript-eslint/no-non-null-assertion", "severity": 2, "message": "Forbidden non-null assertion.", "line": 21, "column": 13, "nodeType": "TSNonNullExpression", "messageId": "noNonNull", "endLine": 21, "endColumn": 41}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'required'.", "line": 30, "column": 48, "nodeType": "Property", "messageId": "sortKeys", "endLine": 30, "endColumn": 59}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'required'.", "line": 31, "column": 46, "nodeType": "Property", "messageId": "sortKeys", "endLine": 31, "endColumn": 57}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'required'.", "line": 32, "column": 47, "nodeType": "Property", "messageId": "sortKeys", "endLine": 32, "endColumn": 58}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'status'.", "line": 35, "column": 5, "nodeType": "Property", "messageId": "sortKeys", "endLine": 35, "endColumn": 16}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'page' should be before 'search'.", "line": 45, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 45, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'limit' should be before 'page'.", "line": 46, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 46, "endColumn": 12}], "suppressedMessages": [], "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import {\n  Controller,\n  Get,\n  Query,\n  HttpStatus,\n  UseGuards,\n} from \"@nestjs/common\";\nimport {\n  ApiTags,\n  ApiOperation,\n  ApiResponse,\n  ApiBearerAuth,\n  ApiOAuth2,\n  ApiQuery,\n} from \"@nestjs/swagger\";\nimport { AuthGuard } from \"../auth/auth.guard\";\nimport { PaginatedSpecificationResponseDto } from \"./dto\";\nimport { SpecificationService } from \"./specification.service\";\n\n@ApiBearerAuth()\n@ApiOAuth2([process.env.AZURE_API_SCOPE!])\n@UseGuards(AuthGuard)\n@ApiTags(\"specifications\")\n@Controller(\"specifications\")\nexport class SpecificationController {\n  public constructor(private readonly specificationService: SpecificationService) {}\n\n  @Get()\n  @ApiOperation({ summary: \"Get paginated list of specification\" })\n  @ApiQuery({ name: \"search\", required: false, description: \"Search by label or property\" })\n  @ApiQuery({ name: \"page\", required: false, description: \"Page number\" })\n  @ApiQuery({ name: \"limit\", required: false, description: \"Items per page\" })\n  @ApiResponse({\n    status: HttpStatus.OK,\n    description: \"Specification criteria retrieved successfully\",\n    type: PaginatedSpecificationResponseDto,\n  })\n  public async findAll(\n    @Query(\"search\") search?: string,\n    @Query(\"page\") page?: number,\n    @Query(\"limit\") limit?: number,\n  ): Promise<PaginatedSpecificationResponseDto> {\n    return this.specificationService.findAll({\n      search,\n      page,\n      limit,\n    });\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/specification.module.ts", "messages": [{"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'exports' should be before 'providers'.", "line": 11, "column": 3, "nodeType": "Property", "messageId": "sortKeys", "endLine": 11, "endColumn": 10}], "suppressedMessages": [], "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { <PERSON><PERSON><PERSON> } from \"@nestjs/common\";\nimport { PrismaService } from \"../prisma.service\";\nimport { SpecificationRepository } from \"./repositories/specification.repository\";\nimport { SpecificationController } from \"./specification.controller\";\nimport { SpecificationService } from \"./specification.service\";\nimport { AuthService } from \"@/auth/auth.service\";\n\n@Module({\n  controllers: [SpecificationController],\n  providers: [SpecificationService, SpecificationRepository, PrismaService, AuthService],\n  exports: [SpecificationService],\n})\nexport class SpecificationModule {}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/specification/specification.service.ts", "messages": [{"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'page' should be before 'search'.", "line": 22, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 22, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'limit' should be before 'page'.", "line": 23, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 23, "endColumn": 12}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'page' should be before 'total'.", "line": 29, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 29, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'limit' should be before 'page'.", "line": 30, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 30, "endColumn": 12}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'label' should be before 'property'.", "line": 38, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 38, "endColumn": 12}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'type' should be before 'unit'.", "line": 40, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 40, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'createdAt' should be before 'type'.", "line": 41, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 41, "endColumn": 16}], "suppressedMessages": [], "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Injectable } from \"@nestjs/common\";\nimport { createPaginatedResponse, normalizePaginationParameters } from \"../common/utils\";\nimport { PaginatedSpecificationResponseDto, SpecificationResponseDto } from \"./dto\";\nimport { SpecificationRepository } from \"./repositories/specification.repository\";\nimport type { Specification } from \"@/generated/prisma\";\n\n@Injectable()\nexport class SpecificationService {\n  public constructor(\n    private readonly specificationRepository: SpecificationRepository,\n  ) {}\n\n  public async findAll(filters: {\n    search?: string\n    page?: number\n    limit?: number\n  }): Promise<PaginatedSpecificationResponseDto> {\n    const { page, limit } = normalizePaginationParameters(filters.page, filters.limit);\n\n    const result = await this.specificationRepository.findAll({\n      search: filters.search,\n      page,\n      limit,\n    });\n\n    return createPaginatedResponse({\n      data: result.data.map(spec => this.mapToResponseDto(spec)),\n      total: result.total,\n      page: result.page,\n      limit: result.limit,\n    });\n  }\n\n  private mapToResponseDto(specification: Specification): SpecificationResponseDto {\n    return {\n      id: specification.id,\n      property: specification.property,\n      label: specification.label,\n      unit: specification.unit,\n      type: specification.type,\n      createdAt: specification.createdAt,\n      updatedAt: specification.updatedAt,\n    };\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/supplier/dto/supplier.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/supplier/repositories/supplier.repository.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/supplier/supplier.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/supplier/supplier.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/dto/create-user.dto.ts", "messages": [{"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'example'.", "line": 5, "column": 67, "nodeType": "Property", "messageId": "sortKeys", "endLine": 5, "endColumn": 78}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property departmentId.", "line": 7, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 7, "endColumn": 15, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [262, 262], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [262, 262], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [262, 262], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property email.", "line": 11, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 11, "endColumn": 8, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [351, 351], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [351, 351], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [351, 351], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'example'.", "line": 13, "column": 67, "nodeType": "Property", "messageId": "sortKeys", "endLine": 13, "endColumn": 78}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property locationId.", "line": 15, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 15, "endColumn": 13, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [477, 477], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [477, 477], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [477, 477], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property name.", "line": 20, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 20, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [572, 572], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [572, 572], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [572, 572], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'description' should be before 'example'.", "line": 22, "column": 67, "nodeType": "Property", "messageId": "sortKeys", "endLine": 22, "endColumn": 78}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property roleId.", "line": 24, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 24, "endColumn": 9, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [693, 693], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [693, 693], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [693, 693], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { ApiProperty } from \"@nestjs/swagger\";\nimport { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString, IsUUID } from \"class-validator\";\n\nexport class CreateUserDto {\n  @ApiProperty({ example: \"921f82e3-e487-4d07-a585-6e86fb988fdc\", description: \"Department ID\" })\n  @IsUUID()\n  departmentId: string;\n\n  @ApiProperty({ example: \"<EMAIL>\" })\n  @IsEmail()\n  email: string;\n\n  @ApiProperty({ example: \"9c5ae51c-7ca3-4fa6-a8e3-c8df27782ae6\", description: \"Location ID\" })\n  @IsUUID()\n  locationId: string;\n\n  @ApiProperty({ example: \"Omar Press\" })\n  @IsNotEmpty()\n  @IsString()\n  name: string;\n\n  @ApiProperty({ example: \"51c4f5ec-72f5-45b4-bec2-7e4be6a7b548\", description: \"Role ID\" })\n  @IsUUID()\n  roleId: string;\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/dto/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/dto/paginated-user-response.dto.ts", "messages": [{"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property data.", "line": 7, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 7, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [294, 294], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [294, 294], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [294, 294], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property meta.", "line": 10, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 10, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [402, 402], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [402, 402], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [402, 402], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { ApiProperty } from \"@nestjs/swagger\";\nimport { PaginationMetaDto } from \"../../common/dto/pagination-meta.dto\";\nimport { UserResponseDto } from \"./user-response.dto\";\n\nexport class PaginatedUserResponseDto {\n  @ApiProperty({ description: \"Array of users\", type: [UserResponseDto] })\n  data: UserResponseDto[];\n\n  @ApiProperty({ description: \"Pagination metadata\", type: PaginationMetaDto })\n  meta: PaginationMetaDto;\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/dto/update-user.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/dto/user-filter.dto.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/dto/user-response.dto.ts", "messages": [{"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property id.", "line": 5, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 5, "endColumn": 5, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [149, 149], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [149, 149], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [149, 149], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property name.", "line": 8, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 8, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [206, 206], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [206, 206], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [206, 206], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property email.", "line": 11, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 11, "endColumn": 8, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [274, 274], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [274, 274], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [274, 274], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property location.", "line": 14, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 14, "endColumn": 11, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [337, 337], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [337, 337], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [337, 337], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property department.", "line": 17, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 17, "endColumn": 13, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [400, 400], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [400, 400], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [400, 400], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on class property role.", "line": 20, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 20, "endColumn": 7, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [460, 460], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [460, 460], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [460, 460], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { ApiProperty } from \"@nestjs/swagger\";\n\nexport class UserResponseDto {\n  @ApiProperty({ example: \"550e8400-e29b-41d4-a716-446655440000\" })\n  id: string;\n\n  @ApiProperty({ example: \"Omar Press\" })\n  name: string;\n\n  @ApiProperty({ example: \"<EMAIL>\" })\n  email: string;\n\n  @ApiProperty({ example: \"Paris, France\" })\n  location: string;\n\n  @ApiProperty({ example: \"Technology\" })\n  department: string;\n\n  @ApiProperty({ example: \"ADMIN\" })\n  role: string;\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/repositories/index.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/repositories/user.repository.ts", "messages": [{"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition constructor.", "line": 55, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 55, "endColumn": 14, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [945, 945], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [945, 945], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [945, 945], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition create.", "line": 57, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 57, "endColumn": 15, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [1003, 1003], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [1003, 1003], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [1003, 1003], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'location' should be before 'role'.", "line": 62, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 62, "endColumn": 17}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'department' should be before 'location'.", "line": 63, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 63, "endColumn": 19}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findAll.", "line": 68, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 68, "endColumn": 16, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [1242, 1242], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [1242, 1242], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [1242, 1242], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/no-explicit-any", "severity": 2, "message": "Unexpected any. Specify a different type.", "line": 69, "column": 18, "nodeType": "TSAnyKeyword", "messageId": "unexpectedAny", "endLine": 69, "endColumn": 21, "suggestions": [{"messageId": "suggestUnknown", "fix": {"range": [1348, 1351], "text": "unknown"}, "desc": "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct."}, {"messageId": "suggestNever", "fix": {"range": [1348, 1351], "text": "never"}, "desc": "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of."}]}, {"ruleId": "@typescript-eslint/prefer-nullish-coalescing", "severity": 2, "message": "Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.", "line": 70, "column": 31, "nodeType": "Punctuator", "messageId": "preferNullishOverOr", "endLine": 70, "endColumn": 33, "suggestions": [{"messageId": "<PERSON><PERSON><PERSON><PERSON>", "data": {"equals": ""}, "fix": {"range": [1388, 1390], "text": "??"}, "desc": "Fix to nullish coalescing operator (`??`)."}]}, {"ruleId": "@typescript-eslint/prefer-nullish-coalescing", "severity": 2, "message": "Prefer using nullish coalescing operator (`??`) instead of a logical or (`||`), as it is a safer operator.", "line": 71, "column": 33, "nodeType": "Punctuator", "messageId": "preferNullishOverOr", "endLine": 71, "endColumn": 35, "suggestions": [{"messageId": "<PERSON><PERSON><PERSON><PERSON>", "data": {"equals": ""}, "fix": {"range": [1426, 1428], "text": "??"}, "desc": "Fix to nullish coalescing operator (`??`)."}]}, {"ruleId": "@typescript-eslint/no-unsafe-member-access", "severity": 2, "message": "Unsafe member access .locationId on an `any` value.", "line": 75, "column": 13, "nodeType": "Identifier", "messageId": "unsafeMemberExpression", "endLine": 75, "endColumn": 23}, {"ruleId": "@typescript-eslint/no-unsafe-member-access", "severity": 2, "message": "Unsafe member access .departmentId on an `any` value.", "line": 79, "column": 13, "nodeType": "Identifier", "messageId": "unsafeMemberExpression", "endLine": 79, "endColumn": 25}, {"ruleId": "@typescript-eslint/no-unsafe-member-access", "severity": 2, "message": "Unsafe member access .roleId on an `any` value.", "line": 83, "column": 13, "nodeType": "Identifier", "messageId": "unsafeMemberExpression", "endLine": 83, "endColumn": 19}, {"ruleId": "@typescript-eslint/no-unsafe-member-access", "severity": 2, "message": "Unsafe member access .role on an `any` value.", "line": 87, "column": 13, "nodeType": "Identifier", "messageId": "unsafeMemberExpression", "endLine": 87, "endColumn": 17}, {"ruleId": "@typescript-eslint/no-unsafe-member-access", "severity": 2, "message": "Unsafe member access .OR on an `any` value.", "line": 93, "column": 13, "nodeType": "Identifier", "messageId": "unsafeMemberExpression", "endLine": 93, "endColumn": 15}, {"ruleId": "@typescript-eslint/no-unsafe-assignment", "severity": 2, "message": "Unsafe assignment of an `any` value.", "line": 101, "column": 9, "nodeType": "Property", "messageId": "anyAssignment", "endLine": 101, "endColumn": 14}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'include' should be before 'where'.", "line": 102, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 102, "endColumn": 16}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'location' should be before 'role'.", "line": 104, "column": 11, "nodeType": "Property", "messageId": "sortKeys", "endLine": 104, "endColumn": 19}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'department' should be before 'location'.", "line": 105, "column": 11, "nodeType": "Property", "messageId": "sortKeys", "endLine": 105, "endColumn": 21}, {"ruleId": "@typescript-eslint/no-unsafe-assignment", "severity": 2, "message": "Unsafe assignment of an `any` value.", "line": 111, "column": 32, "nodeType": "Property", "messageId": "anyAssignment", "endLine": 111, "endColumn": 37}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'page' should be before 'total'.", "line": 117, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 117, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'limit' should be before 'page'.", "line": 118, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 118, "endColumn": 12}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findById.", "line": 122, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 122, "endColumn": 17, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [2415, 2415], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [2415, 2415], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [2415, 2415], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'include' should be before 'where'.", "line": 125, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 125, "endColumn": 14}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'location' should be before 'role'.", "line": 127, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 127, "endColumn": 17}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'department' should be before 'location'.", "line": 128, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 128, "endColumn": 19}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findOneOrThrow.", "line": 133, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 133, "endColumn": 23, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [2652, 2652], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [2652, 2652], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [2652, 2652], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition findByEmail.", "line": 141, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 141, "endColumn": 20, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [2861, 2861], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [2861, 2861], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [2861, 2861], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition update.", "line": 147, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 147, "endColumn": 15, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [3002, 3002], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [3002, 3002], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [3002, 3002], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'data' should be before 'where'.", "line": 150, "column": 7, "nodeType": "Property", "messageId": "sortKeys", "endLine": 150, "endColumn": 11}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'location' should be before 'role'.", "line": 153, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 153, "endColumn": 17}, {"ruleId": "sort-keys", "severity": 2, "message": "Expected object keys to be in natural ascending order. 'department' should be before 'location'.", "line": 154, "column": 9, "nodeType": "Property", "messageId": "sortKeys", "endLine": 154, "endColumn": 19}, {"ruleId": "@typescript-eslint/explicit-member-accessibility", "severity": 2, "message": "Missing accessibility modifier on method definition delete.", "line": 159, "column": 3, "nodeType": null, "messageId": "missingAccessibility", "endLine": 159, "endColumn": 15, "suggestions": [{"messageId": "addExplicitAccessibility", "data": {"type": "public"}, "fix": {"range": [3274, 3274], "text": "public "}, "desc": "Add 'public' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "private"}, "fix": {"range": [3274, 3274], "text": "private "}, "desc": "Add 'private' accessibility modifier"}, {"messageId": "addExplicitAccessibility", "data": {"type": "protected"}, "fix": {"range": [3274, 3274], "text": "protected "}, "desc": "Add 'protected' accessibility modifier"}]}], "suppressedMessages": [], "errorCount": 31, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "import { Injectable } from \"@nestjs/common\";\nimport { PrismaService } from \"../../prisma.service\";\nimport { PaginationResult } from \"@/common/utils\";\n\nexport interface UserData {\n  id: string\n  name: string\n  email: string\n  locationId: string\n  departmentId: string\n  roleId: string\n}\n\nexport interface UserWithRelations extends UserData {\n  location: {\n    city: string\n    country: string\n  }\n  department: {\n    name: string\n  }\n  role: {\n    code: string\n  }\n}\n\nexport interface CreateUserData {\n  name: string\n  email: string\n  locationId: string\n  departmentId: string\n  roleId: string\n}\n\nexport interface UpdateUserData {\n  name?: string\n  email?: string\n  locationId?: string\n  departmentId?: string\n  roleId?: string\n}\n\nexport interface UserFilters {\n  page?: number\n  limit?: number\n  locationId?: string\n  departmentId?: string\n  roleId?: string\n  roleCode?: string\n  search?: string\n}\n\n@Injectable()\nexport class UserRepository {\n  constructor(private readonly prisma: PrismaService) {}\n\n  async create(userData: CreateUserData): Promise<UserWithRelations> {\n    return this.prisma.user.create({\n      data: userData,\n      include: {\n        role: true,\n        location: true,\n        department: true,\n      },\n    });\n  }\n\n  async findAll(filters: UserFilters = {}): Promise<PaginationResult<UserWithRelations>> {\n    const where: any = {};\n    const page = filters.page || 1;\n    const limit = filters.limit || 10;\n    const skip = (page - 1) * limit;\n\n    if (filters.locationId) {\n      where.locationId = filters.locationId;\n    }\n\n    if (filters.departmentId) {\n      where.departmentId = filters.departmentId;\n    }\n\n    if (filters.roleId) {\n      where.roleId = filters.roleId;\n    }\n\n    if (filters.roleCode) {\n      where.role = {\n        code: filters.roleCode,\n      };\n    }\n\n    if (filters.search) {\n      where.OR = [\n        { name: { contains: filters.search, mode: \"insensitive\" } },\n        { email: { contains: filters.search, mode: \"insensitive\" } },\n      ];\n    }\n\n    const [data, total] = await Promise.all([\n      this.prisma.user.findMany({\n        where,\n        include: {\n          role: true,\n          location: true,\n          department: true,\n        },\n        orderBy: { name: \"asc\" },\n        skip,\n        take: limit,\n      }),\n      this.prisma.user.count({ where }),\n    ]);\n\n    return {\n      data,\n      total,\n      page,\n      limit,\n    };\n  }\n\n  async findById(id: string): Promise<UserWithRelations | null> {\n    return this.prisma.user.findUnique({\n      where: { id },\n      include: {\n        role: true,\n        location: true,\n        department: true,\n      },\n    });\n  }\n\n  async findOneOrThrow(id: string): Promise<UserWithRelations> {\n    const user = await this.findById(id);\n    if (!user) {\n      throw new Error(`User with ID '${id}' not found`);\n    }\n    return user;\n  }\n\n  async findByEmail(email: string): Promise<UserData | null> {\n    return this.prisma.user.findUnique({\n      where: { email },\n    });\n  }\n\n  async update(id: string, userData: UpdateUserData): Promise<UserWithRelations> {\n    return this.prisma.user.update({\n      where: { id },\n      data: userData,\n      include: {\n        role: true,\n        location: true,\n        department: true,\n      },\n    });\n  }\n\n  async delete(id: string): Promise<void> {\n    await this.prisma.user.delete({\n      where: { id },\n    });\n  }\n}\n", "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/user.controller.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/user.module.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}, {"filePath": "/Users/<USER>/Projects/lmesh/materiact-backend/src/user/user.service.ts", "messages": [], "suppressedMessages": [], "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": [{"ruleId": "@typescript-eslint/no-empty-interface", "replacedBy": ["@typescript-eslint/no-empty-object-type"], "info": {"deprecatedSince": "8.0.0", "replacedBy": [{"rule": {"name": "@typescript-eslint/no-empty-object-type", "url": "https://typescript-eslint.io/rules/no-empty-object-type"}}], "url": "https://github.com/typescript-eslint/typescript-eslint/pull/8977"}}]}]